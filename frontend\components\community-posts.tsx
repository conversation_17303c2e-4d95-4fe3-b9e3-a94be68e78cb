"use client";

import { useState, useEffect } from "react";
import { CreatePostForm } from "@/components/create-post-form";
import { PostCard } from "@/components/post-card";
import { getPosts } from "@/actions/post.actions";
import { User } from "@prisma/client";
import { Loader } from "lucide-react";

type PostWithUser = {
  id: string;
  title: string;
  content: string;
  likes: number;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  user: {
    id: string;
    name: string | null;
    email: string | null;
    image: string | null;
  };
};

interface CommunityPostsProps {
  user: User;
}

export function CommunityPosts({ user }: CommunityPostsProps) {
  const [posts, setPosts] = useState<PostWithUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");

  const fetchPosts = async () => {
    try {
      setIsLoading(true);
      setError("");
      const result = await getPosts();

      if (result.success && result.posts) {
        setPosts(result.posts);
      } else {
        setError(result.error || "Failed to fetch posts");
      }
    } catch {
      setError("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchPosts();
  }, []);

  const handlePostCreated = () => {
    fetchPosts();
  };

  const handlePostDeleted = () => {
    fetchPosts();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader className="w-6 h-6 animate-spin text-muted-foreground" />
        <span className="ml-2 text-muted-foreground">
          Loading community posts...
        </span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-foreground">Community Posts</h1>
        <p className="text-muted-foreground">
          Share updates and stay informed about forest activities and wildlife
          observations
        </p>
      </div>

      {/* Create Post Form */}
      <CreatePostForm user={user} onPostCreated={handlePostCreated} />

      {/* Error Message */}
      {error && (
        <div className="bg-destructive/10 text-destructive border border-destructive/20 rounded-md p-4">
          {error}
        </div>
      )}

      {/* Posts List */}
      <div className="space-y-4">
        {posts.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-muted-foreground">
              <p className="text-lg mb-2">No posts yet</p>
              <p className="text-sm">
                Be the first to share an update with the community!
              </p>
            </div>
          </div>
        ) : (
          posts.map((post) => (
            <PostCard
              key={post.id}
              post={post}
              currentUser={user}
              onPostDeleted={handlePostDeleted}
            />
          ))
        )}
      </div>
    </div>
  );
}
