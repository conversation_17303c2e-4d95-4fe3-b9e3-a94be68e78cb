"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { createPost } from "@/actions/post.actions";
import { User } from "@prisma/client";

interface CreatePostFormProps {
  user: User;
  onPostCreated?: () => void;
}

export function CreatePostForm({ user, onPostCreated }: CreatePostFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [message, setMessage] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!title.trim() || !content.trim()) {
      setMessage("Please fill in both title and content");
      return;
    }

    setIsLoading(true);
    setMessage("");

    try {
      const formData = new FormData();
      formData.append("title", title);
      formData.append("content", content);
      formData.append("userId", user.id);

      const result = await createPost(formData);

      if (result.success) {
        setMessage("Your post has been created successfully!");
        setTitle("");
        setContent("");
        onPostCreated?.();
      } else {
        setMessage(result.error || "Failed to create post");
      }
    } catch {
      setMessage("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="border-border">
      <CardHeader>
        <CardTitle className="text-foreground">Create New Post</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {message && (
            <div
              className={`text-sm p-3 rounded-md ${
                message.includes("success")
                  ? "bg-primary/10 text-primary border border-primary/20"
                  : "bg-destructive/10 text-destructive border border-destructive/20"
              }`}
            >
              {message}
            </div>
          )}

          <div className="space-y-2">
            <Label
              htmlFor="title"
              className="text-sm font-medium text-foreground"
            >
              Title
            </Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Share what's happening in the forest..."
              className="bg-background border-border text-foreground placeholder:text-muted-foreground"
              disabled={isLoading}
            />
          </div>

          <div className="space-y-2">
            <Label
              htmlFor="content"
              className="text-sm font-medium text-foreground"
            >
              Content
            </Label>
            <Textarea
              id="content"
              value={content}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                setContent(e.target.value)
              }
              placeholder="Tell the community about recent wildlife activity, forest updates, or important notices..."
              className="bg-background border-border text-foreground placeholder:text-muted-foreground min-h-[120px] resize-none"
              disabled={isLoading}
            />
          </div>

          <Button
            type="submit"
            disabled={isLoading || !title.trim() || !content.trim()}
            className="bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50"
          >
            {isLoading ? "Posting..." : "Post Update"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
