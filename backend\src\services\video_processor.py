"""
Video processing service for wildlife detection.
"""

import cv2
import os
import subprocess
from pathlib import Path
from typing import Tuple, List, Dict, Any, Optional

from ..config.settings import (
    DAY_CLASS_TO_ANIMAL,
    THERMAL_CLASS_TO_ANIMAL,
    DETECTION_COLORS,
    STATIC_DIR,
    ENABLE_BROWSER_OPTIMIZATION,
    REMOVE_ORIGINAL_AFTER_OPTIMIZATION,
)
from ..core.model_manager import model_manager
from ..models.schemas import Detection, FrameDetection


class VideoProcessor:
    """Handles video processing for wildlife detection."""

    def __init__(self):
        self.day_model = None
        self.thermal_model = None

    def _ensure_models_loaded(self):
        """Ensure models are loaded. If already loaded at startup, skip reloading."""
        if self.day_model is None or self.thermal_model is None:
            print("⚠️ Models not preloaded, loading them now...")
            self.day_model, self.thermal_model = model_manager.load_models()
        else:
            print("✅ Using preloaded models from startup")

    def process_video(
        self, temp_video_path: str, video_class: str, video_uuid: Optional[str] = None
    ) -> Tuple[List[Dict[str, Any]], str]:
        """
        Process video for wildlife detection.

        Args:
            temp_video_path: Relative path to the video file
            video_class: "Daylight" or "Thermal"
            video_uuid: Optional UUID for unique output filename

        Returns:
            tuple: (results_list, output_path)
        """
        try:
            self._ensure_models_loaded()

            # Open video file
            video_path = STATIC_DIR / temp_video_path.lstrip("/")
            cap = cv2.VideoCapture(str(video_path))

            if not cap.isOpened():
                raise ValueError(f"Could not open video file: {temp_video_path}")

            # Get video properties
            fps = int(cap.get(cv2.CAP_PROP_FPS))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            if fps <= 0 or width <= 0 or height <= 0:
                raise ValueError("Invalid video properties")

            print(f"Video info: {width}x{height}, {fps} FPS, {total_frames} frames")

            # Create output directory if it doesn't exist
            output_dir = STATIC_DIR / "out"
            os.makedirs(output_dir, exist_ok=True)

            # Generate unique output filename using UUID
            if video_uuid:
                output_filename = (
                    f"{video_uuid}_processed.mp4"  # Use MP4 as primary format
                )
            else:
                output_filename = "output_video.mp4"

            output_path = f"/out/{output_filename}"

            # Use MP4 format as primary (better compatibility)
            fourcc = cv2.VideoWriter_fourcc(*"mp4v")
            out = cv2.VideoWriter(
                str(STATIC_DIR / output_path.lstrip("/")), fourcc, fps, (width, height)
            )

            if not out.isOpened():
                # Fallback to different codec if mp4v doesn't work
                print("⚠️ mp4v codec failed, trying XVID...")
                fourcc = cv2.VideoWriter_fourcc(*"XVID")
                out = cv2.VideoWriter(
                    str(STATIC_DIR / output_path.lstrip("/")),
                    fourcc,
                    fps,
                    (width, height),
                )

                if not out.isOpened():
                    # Last fallback to H264
                    print("⚠️ XVID codec failed, trying H264...")
                    fourcc = cv2.VideoWriter_fourcc(*"H264")
                    out = cv2.VideoWriter(
                        str(STATIC_DIR / output_path.lstrip("/")),
                        fourcc,
                        fps,
                        (width, height),
                    )

            result = []
            frame_count = 0
            print("Prediction start")

            # Select model based on video class
            model = self.day_model if video_class == "Daylight" else self.thermal_model
            class_dict = (
                DAY_CLASS_TO_ANIMAL
                if video_class == "Daylight"
                else THERMAL_CLASS_TO_ANIMAL
            )

            while cap.isOpened():
                success, frame = cap.read()

                if not success:
                    print("End of video or failed to read frame")
                    break

                frame_count += 1

                # Resize frame to maintain aspect ratio
                frame = cv2.resize(frame, (width, height))

                # Run model inference
                results = model(frame)
                print(f"Frame {frame_count}: Processing complete")

                # Visualize results on frame
                annotated_frame = self._plot_bounding_boxes(
                    results=results, frame=frame, class_dict=class_dict
                )
                out.write(annotated_frame)

                # Process detections if any
                if len(results.xyxy[0]) > 0:
                    try:
                        classes = results.pandas().xyxy[0]["class"]
                        confidences = results.pandas().xyxy[0]["confidence"]
                        print(
                            f"Frame {frame_count} detections - Classes: {classes.values}, Confidences: {confidences.values}"
                        )

                        frame_detections = {
                            "frame_number": frame_count,
                            "detections": [],
                        }

                        for class_id, confidence in zip(classes, confidences):
                            obj = {
                                "class_id": int(class_id),
                                "class_name": class_dict.get(int(class_id), "Unknown"),
                                "confidence": float(confidence),
                            }
                            frame_detections["detections"].append(obj)

                        result.append(frame_detections)
                    except Exception as e:
                        print(
                            f"Error processing detections for frame {frame_count}: {e}"
                        )

            cap.release()
            out.release()
            cv2.destroyAllWindows()

            print(
                f"Processing complete. Total frames: {frame_count}, Detections in {len(result)} frames"
            )

            # Create browser-optimized version if enabled
            if ENABLE_BROWSER_OPTIMIZATION:
                try:
                    optimized_path = self._create_browser_optimized_video(
                        output_path, video_uuid
                    )
                    print(f"Browser-optimized video created: {optimized_path}")
                    return result, optimized_path
                except Exception as e:
                    print(f"Warning: Could not create browser-optimized video: {e}")
                    print("Returning original video")
                    return result, output_path
            else:
                print("Browser optimization disabled, returning original video")
                return result, output_path

        except Exception as e:
            print(f"Error in process_video: {e}")
            # Clean up resources
            try:
                cap.release()
                out.release()
                cv2.destroyAllWindows()
            except:
                pass
            raise e

    def _create_browser_optimized_video(
        self, original_path: str, video_uuid: Optional[str] = None
    ) -> str:
        """
        Create a browser-optimized version of the processed video.

        Args:
            original_path: Path to the original processed video
            video_uuid: UUID for unique filename

        Returns:
            str: Path to the optimized video
        """
        try:
            # Generate optimized filename
            if video_uuid:
                optimized_filename = f"{video_uuid}_processed_optimized.mp4"
            else:
                optimized_filename = "output_video_optimized.mp4"

            optimized_path = f"/out/{optimized_filename}"

            # Full paths for FFmpeg
            input_full_path = str(STATIC_DIR / original_path.lstrip("/"))
            output_full_path = str(STATIC_DIR / optimized_path.lstrip("/"))

            # FFmpeg command for browser optimization
            ffmpeg_cmd = [
                "ffmpeg",
                "-i",
                input_full_path,
                "-c:v",
                "libx264",  # H.264 codec
                "-profile:v",
                "baseline",  # Maximum compatibility
                "-level",
                "3.0",  # Wide device support
                "-movflags",
                "+faststart",  # Move metadata to beginning
                "-pix_fmt",
                "yuv420p",  # Compatible pixel format
                "-crf",
                "23",  # Good quality/size balance
                "-y",  # Overwrite output file
                output_full_path,
            ]

            print(f"Creating browser-optimized video with FFmpeg...")
            result = subprocess.run(
                ffmpeg_cmd,
                capture_output=True,
                text=True,
                timeout=300,  # 5 minute timeout
            )

            if result.returncode == 0:
                print("✅ Browser optimization successful")

                # Check if optimized file was created and has reasonable size
                if (
                    os.path.exists(output_full_path)
                    and os.path.getsize(output_full_path) > 1000
                ):
                    # Optionally remove the original non-optimized version to save space
                    if REMOVE_ORIGINAL_AFTER_OPTIMIZATION:
                        try:
                            os.remove(input_full_path)
                            print(f"Removed original video: {input_full_path}")
                        except Exception as e:
                            print(f"Warning: Could not remove original video: {e}")
                    else:
                        print(f"Keeping original video: {input_full_path}")

                    return optimized_path
                else:
                    raise Exception("Optimized video file not created or too small")
            else:
                raise Exception(f"FFmpeg failed: {result.stderr}")

        except subprocess.TimeoutExpired:
            raise Exception("FFmpeg optimization timed out")
        except FileNotFoundError:
            raise Exception(
                "FFmpeg not found. Please ensure FFmpeg is installed and in PATH"
            )
        except Exception as e:
            raise Exception(f"Browser optimization failed: {str(e)}")

    def _plot_bounding_boxes(self, results, frame, class_dict):
        """Plot bounding boxes on frame."""
        for box in results.xyxy[0]:
            xA, yA, xB, yB, confidence, class_id = box
            class_id = int(class_id)
            class_name = class_dict.get(int(class_id), "Unknown")
            # Define a unique color for each class
            color = self._get_color(class_id)
            xA = int(xA)
            xB = int(xB)
            yA = int(yA)
            yB = int(yB)
            # Draw the bounding box with the class-specific color
            cv2.rectangle(frame, (xA, yA), (xB, yB), color, 2)

            # Add text label with class name and confidence
            label = f"{class_name}: {confidence:.2f}"
            y = yA - 15 if yA - 15 > 15 else yA + 15
            cv2.putText(frame, label, (xA, y), cv2.FONT_HERSHEY_SIMPLEX, 1, color, 2)

        return frame

    def _get_color(self, class_id):
        """Get color for class ID."""
        return DETECTION_COLORS[class_id % len(DETECTION_COLORS)]


# Global video processor instance
video_processor = VideoProcessor()
