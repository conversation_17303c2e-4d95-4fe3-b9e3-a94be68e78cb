import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";

export async function POST() {
  try {
    // 1. Authenticate the user
    const sessionData = await auth();

    if (!sessionData?.user?.id) {
      return NextResponse.json({ 
        error: "Authentication required",
        message: "You must be logged in to create an upload session" 
      }, { status: 401 });
    }
    
    const userId = sessionData.user.id;

    // 2. Verify user exists in database
    const user = await db.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      return NextResponse.json({ 
        error: "User not found",
        message: "Your user account was not found in the database" 
      }, { status: 404 });
    }

    // 3. Mark any existing PENDING sessions as EXPIRED to ensure one session at a time
    // But allow multiple recordings within the same session
    await db.uploadSession.updateMany({
      where: {
        userId: userId,
        status: "PENDING",
        // Only expire sessions older than 1 hour to allow for long recordings
        createdAt: {
          lt: new Date(Date.now() - 60 * 60 * 1000) // 1 hour ago
        }
      },
      data: {
        status: "EXPIRED"
      }
    });

    // 4. Create a new UploadSession in the database
    const uploadSession = await db.uploadSession.create({
      data: {
        userId: userId,
        status: "PENDING",
      },
    });

    console.log("Created new upload session:", uploadSession.id);

    // 5. Return the unique ID with additional info
    return NextResponse.json({ 
      id: uploadSession.id,
      uploadUrl: `/video/upload/${uploadSession.id}`,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
      message: "Upload session created successfully"
    });
  } catch (error) {
    console.error("Create Upload Session Error:", error);
    return NextResponse.json({ 
      error: "Internal Server Error",
      message: "Failed to create upload session. Please try again."
    }, { status: 500 });
  }
}
