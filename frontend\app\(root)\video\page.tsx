"use client";

import { useEffect, useState } from "react";
import QRC<PERSON> from "react-qr-code";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import VideoGallery from "@/components/shared/video-gallery";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";

export default function VideoPage() {
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const createNewSession = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch("/api/upload-session", {
        method: "POST",
      });
      if (!response.ok) {
        throw new Error("Failed to create upload session. Please make sure you are logged in.");
      }
      const data = await response.json();
      setSessionId(data.id);
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError("An unknown error occurred.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Create a new session every time the page loads
    createNewSession();
  }, []);

  const getUploadUrl = () => {
    if (typeof window !== "undefined" && sessionId) {
      return `${window.location.origin}/video/upload/${sessionId}`;
    }
    return "";
  };

  const renderQrCode = () => {
    if (isLoading) {
      return (
        <div className="flex flex-col items-center justify-center gap-4">
            <Skeleton className="h-[256px] w-[256px]"/>
            <Skeleton className="h-4 w-64" />
        </div>
      );
    }

    if (error) {
      return <p className="text-red-500 text-center">{error}</p>;
    }

    const uploadUrl = getUploadUrl();
    if (sessionId && uploadUrl) {
      return (
        <div className="flex flex-col items-center justify-center gap-4 p-4 bg-white rounded-lg">
          <QRCode value={uploadUrl} size={256} />
          <p className="text-center text-sm text-gray-600 mt-2">
            Scan this code with your mobile device to upload a video.
          </p>
        </div>
      );
    }
    
    return null;
  };

  return (
    <div className="container mx-auto py-10 space-y-12">
      <Card className="max-w-2xl mx-auto">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">Record Video from Mobile</CardTitle>
          <CardDescription>
            Each session generates a unique QR code. Once you stop recording, this link expires and cannot be reused.
            Scan with your mobile device to start recording.
          </CardDescription>
        </CardHeader>
        <CardContent>
            {renderQrCode()}
            {sessionId && (
              <div className="mt-6 text-center">
                <Button 
                  onClick={createNewSession}
                  variant="outline"
                  className="flex items-center gap-2"
                  disabled={isLoading}
                >
                  <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
                  Generate New QR Code
                </Button>
                <p className="text-xs text-gray-500 mt-2">
                  Generate a new QR code to start a fresh recording session
                </p>
              </div>
            )}
        </CardContent>
      </Card>

      <Separator />

      <div>
        <h2 className="text-3xl font-bold text-center mb-8">Current Session Videos</h2>
        <p className="text-center text-gray-600 mb-6">
          Videos from your current recording session will appear here
        </p>
        {sessionId && <VideoGallery sessionId={sessionId} />}
      </div>
    </div>
  );
}
