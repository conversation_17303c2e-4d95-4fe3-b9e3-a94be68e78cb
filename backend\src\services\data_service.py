"""
Data service for loading sample/test data.
"""

from typing import List, Dict, Any


class DataService:
    """Handles data loading operations."""

    @staticmethod
    def load_sample_data() -> List[Dict[str, Any]]:
        """
        Load sample detection data for testing purposes.

        Returns:
            List of frame detection data
        """
        # This is the same data from your original load_data.py
        return [
            {
                "frame_number": 1,
                "detections": [
                    {
                        "class_id": 4,
                        "class_name": "<PERSON>",
                        "confidence": 0.7836139798164368,
                    },
                    {
                        "class_id": 4,
                        "class_name": "<PERSON>",
                        "confidence": 0.6947987079620361,
                    },
                    {
                        "class_id": 4,
                        "class_name": "<PERSON>",
                        "confidence": 0.6800622344017029,
                    },
                    {
                        "class_id": 2,
                        "class_name": "Zebra",
                        "confidence": 0.6797096133232117,
                    },
                    {
                        "class_id": 4,
                        "class_name": "<PERSON>",
                        "confidence": 0.6068112850189209,
                    },
                    {
                        "class_id": 4,
                        "class_name": "<PERSON>",
                        "confidence": 0.5085324048995972,
                    },
                    {
                        "class_id": 4,
                        "class_name": "<PERSON>",
                        "confidence": 0.2607611417770386,
                    },
                ],
            },
            # Adding just a few more samples for brevity - you can add the full dataset
            {
                "frame_number": 2,
                "detections": [
                    {
                        "class_id": 4,
                        "class_name": "Deer",
                        "confidence": 0.7828310132026672,
                    },
                    {
                        "class_id": 4,
                        "class_name": "Deer",
                        "confidence": 0.6967573761940002,
                    },
                    {
                        "class_id": 2,
                        "class_name": "Zebra",
                        "confidence": 0.6794367432594299,
                    },
                    {
                        "class_id": 4,
                        "class_name": "Deer",
                        "confidence": 0.6776313781738281,
                    },
                    {
                        "class_id": 4,
                        "class_name": "Deer",
                        "confidence": 0.6104915142059326,
                    },
                ],
            },
            {
                "frame_number": 12,
                "detections": [
                    {
                        "class_id": 4,
                        "class_name": "Deer",
                        "confidence": 0.8029130101203918,
                    },
                    {
                        "class_id": 2,
                        "class_name": "Zebra",
                        "confidence": 0.7589782476425171,
                    },
                    {
                        "class_id": 4,
                        "class_name": "Deer",
                        "confidence": 0.6871448755264282,
                    },
                    {
                        "class_id": 4,
                        "class_name": "Deer",
                        "confidence": 0.5193354487419128,
                    },
                    {
                        "class_id": 0,
                        "class_name": "Person",
                        "confidence": 0.34776830673217773,
                    },
                    {
                        "class_id": 4,
                        "class_name": "Deer",
                        "confidence": 0.341743141412735,
                    },
                ],
            },
            {
                "frame_number": 19,
                "detections": [
                    {
                        "class_id": 4,
                        "class_name": "Deer",
                        "confidence": 0.7923390865325928,
                    },
                    {
                        "class_id": 2,
                        "class_name": "Zebra",
                        "confidence": 0.7256152033805847,
                    },
                    {
                        "class_id": 4,
                        "class_name": "Deer",
                        "confidence": 0.6745617985725403,
                    },
                    {
                        "class_id": 10,
                        "class_name": "Fire",
                        "confidence": 0.6720724701881409,
                    },
                    {
                        "class_id": 4,
                        "class_name": "Deer",
                        "confidence": 0.6278819441795349,
                    },
                    {
                        "class_id": 4,
                        "class_name": "Deer",
                        "confidence": 0.48613157868385315,
                    },
                ],
            },
        ]


# Global data service instance
data_service = DataService()
