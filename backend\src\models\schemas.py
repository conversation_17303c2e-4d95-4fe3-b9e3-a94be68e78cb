"""
Data models for wildlife detection system.
"""

from typing import List, Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class Detection:
    """Represents a single detection in a frame."""

    class_id: int
    class_name: str
    confidence: float
    latitude: Optional[float] = None
    longitude: Optional[float] = None


@dataclass
class FrameDetection:
    """Represents detections in a single frame."""

    frame_number: int
    detections: List[Detection]


@dataclass
class VideoProcessingResult:
    """Result of video processing."""

    results: List[FrameDetection]
    output_path: str


@dataclass
class UploadRequest:
    """Video upload request data."""

    video_filename: str
    selected_class: str
    get_alert: bool


def dict_to_detection(detection_dict: Dict[str, Any]) -> Detection:
    """Convert dictionary to Detection object."""
    return Detection(
        class_id=detection_dict["class_id"],
        class_name=detection_dict["class_name"],
        confidence=detection_dict["confidence"],
        latitude=detection_dict.get("latitude"),
        longitude=detection_dict.get("longitude"),
    )


def detection_to_dict(detection: Detection) -> Dict[str, Any]:
    """Convert Detection object to dictionary."""
    result = {
        "class_id": detection.class_id,
        "class_name": detection.class_name,
        "confidence": detection.confidence,
    }
    if detection.latitude is not None:
        result["latitude"] = detection.latitude
    if detection.longitude is not None:
        result["longitude"] = detection.longitude
    return result


def frame_detection_to_dict(frame_detection: FrameDetection) -> Dict[str, Any]:
    """Convert FrameDetection object to dictionary."""
    return {
        "frame_number": frame_detection.frame_number,
        "detections": [
            detection_to_dict(detection) for detection in frame_detection.detections
        ],
    }
