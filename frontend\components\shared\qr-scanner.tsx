"use client";

import { Scanner } from "@yudiel/react-qr-scanner";
import { useState } from "react";

interface QrScannerProps {
  onScanSuccess: (result: string) => void;
}

export default function QrScanner({ onScanSuccess }: QrScannerProps) {
  const [error, setError] = useState<string | null>(null);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleScan = (result: any) => {
    // The result from this library is an array of detected codes
    if (result && result.length > 0 && result[0].rawValue) {
      onScanSuccess(result[0].rawValue);
    }
  };
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleError = (err: any) => {
    console.error(err);
    // The library's onError is called for various reasons, including permission denial.
    // A robust solution would involve checking navigator.permissions, but for now,
    // a clear message is the most important thing.
    setError(
      "Could not access the camera. Please ensure you have a camera connected and have granted permission in your browser settings. You might need to refresh the page."
    );
  };

  return (
    <div className="w-full max-w-md mx-auto border-2 border-dashed rounded-lg p-4">
      <Scanner
        onScan={handleScan}
        onError={handleError}
        components={{
          finder: true, // Show a finder box
        }}
        styles={{
          container: { width: "100%" },
        }}
      />
      {error && <p className="text-red-500 mt-4 text-center">{error}</p>}
    </div>
  );
}
