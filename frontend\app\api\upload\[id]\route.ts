import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { v2 as cloudinary } from "cloudinary";

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

// Detailed Cloudinary config check
console.log('Cloudinary config check:', {
  cloud_name: !!process.env.CLOUDINARY_CLOUD_NAME,
  api_key: !!process.env.CLOUDINARY_API_KEY,
  api_secret: !!process.env.CLOUDINARY_API_SECRET,
  cloud_name_value: process.env.CLOUDINARY_CLOUD_NAME ? 'SET' : 'MISSING',
  api_key_value: process.env.CLOUDINARY_API_KEY ? 'SET' : 'MISSING',
  api_secret_value: process.env.CLOUDINARY_API_SECRET ? 'SET' : 'MISSING',
});

// Validate Cloudinary configuration
if (!process.env.CLOUDINARY_CLOUD_NAME || !process.env.CLOUDINARY_API_KEY || !process.env.CLOUDINARY_API_SECRET) {
  console.error('❌ Missing Cloudinary environment variables!');
  console.error('Required: CLOUDINARY_CLOUD_NAME, CLOUDINARY_API_KEY, CLOUDINARY_API_SECRET');
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function uploadToCloudinary(buffer: Buffer, options: Record<string, any>): Promise<any> {
  return new Promise((resolve, reject) => {
    console.log('🚀 Starting Cloudinary upload with options:', {
      ...options,
      // Don't log the actual buffer data
      buffer_size: buffer.length
    });
    
    // Validate Cloudinary config before upload
    if (!process.env.CLOUDINARY_CLOUD_NAME || !process.env.CLOUDINARY_API_KEY || !process.env.CLOUDINARY_API_SECRET) {
      const configError = new Error('Cloudinary configuration missing');
      console.error('❌ Cloudinary config validation failed:', configError);
      return reject(configError);
    }
    
    const uploadStream = cloudinary.uploader.upload_stream(options, (error, result) => {
      if (error) {
        console.error('❌ Cloudinary upload error:', {
          message: error.message,
          name: error.name,
          http_code: error.http_code,
          details: error
        });
        
        // Handle specific WebM format errors
        if (error.message?.includes('Unsupported video format') || 
            error.message?.includes('file format') ||
            error.message?.includes('Invalid video file')) {
          console.log('🔄 Video format issue detected, retrying with basic options...');
          
          // Retry with minimal options for problematic formats
          const retryOptions = {
            resource_type: "video" as const,
            public_id: options.public_id,
            folder: options.folder,
            quality: "auto:low",
          };
          
          const retryStream = cloudinary.uploader.upload_stream(retryOptions, (retryError, retryResult) => {
            if (retryError) {
              console.error('❌ Retry upload also failed:', {
                message: retryError.message,
                name: retryError.name,
                http_code: retryError.http_code
              });
              return reject(retryError);
            }
            console.log('✅ Retry upload succeeded:', retryResult?.public_id);
            resolve(retryResult);
          });
          
          retryStream.end(buffer);
          return;
        }
        
        return reject(error);
      }
      console.log('✅ Cloudinary upload success:', {
        public_id: result?.public_id,
        secure_url: result?.secure_url,
        duration: result?.duration,
        format: result?.format,
        bytes: result?.bytes
      });
      resolve(result);
    });
    
    uploadStream.end(buffer);
  });
}

export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  console.log('=== UPLOAD API DEBUG START ===');
  
  try {
    // 0. Early validation of environment and database
    console.log('🔍 Validating environment and database connection...');
    
    // Check Cloudinary environment variables
    if (!process.env.CLOUDINARY_CLOUD_NAME || !process.env.CLOUDINARY_API_KEY || !process.env.CLOUDINARY_API_SECRET) {
      console.error('❌ Missing Cloudinary environment variables');
      return NextResponse.json({ 
        error: "Server configuration error: Missing Cloudinary credentials" 
      }, { status: 500 });
    }
    
    // Check database connection
    try {
      await db.$connect();
      console.log('✅ Database connection successful');
    } catch (dbError) {
      console.error('❌ Database connection failed:', dbError);
      return NextResponse.json({ 
        error: "Database connection error" 
      }, { status: 500 });
    }
    
    const sessionId = (await params).id;
    console.log('📝 Session ID:', sessionId);

    // 1. Validate the upload session
    const uploadSession = await db.uploadSession.findUnique({
      where: { id: sessionId },
      include: { user: true },
    });

    console.log('📝 Upload session found:', uploadSession ? 'Yes' : 'No');
    console.log('📝 Session status:', uploadSession?.status);
    console.log('📝 Session user:', uploadSession?.user?.name);

    if (!uploadSession) {
      console.log('❌ Invalid upload session');
      return NextResponse.json({ error: "Invalid upload session" }, { status: 404 });
    }

    // Only reject EXPIRED sessions, allow PENDING and COMPLETE for uploads
    if (uploadSession.status === "EXPIRED") {
      console.log('❌ Session expired');
      return NextResponse.json({ error: "This upload session has expired. Please generate a new QR code." }, { status: 410 });
    }
    
    // Check session age regardless of status
    const sessionAge = Date.now() - uploadSession.createdAt.getTime();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    if (sessionAge > maxAge) {
      console.log('❌ Session too old, marking as expired');
      // Mark as expired in database
      await db.uploadSession.update({
        where: { id: sessionId },
        data: { status: "EXPIRED" }
      });
      return NextResponse.json({ error: "Upload session has expired" }, { status: 410 });
    }

    const userId = uploadSession.userId;
    console.log('📝 User ID:', userId);

    // 2. Get FormData and the file
    const formData = await request.formData();
    const file = formData.get("video") as Blob | null;
    const latitude = formData.get("latitude") as string | null;
    const longitude = formData.get("longitude") as string | null;

    console.log('📝 File received:', file ? 'Yes' : 'No');
    console.log('📝 File size:', file?.size, 'bytes');
    console.log('📝 File type:', file?.type);
    console.log('📝 Latitude:', latitude);
    console.log('📝 Longitude:', longitude);

    if (!file) {
      console.log('❌ No video file provided');
      return NextResponse.json({ error: "No video file provided" }, { status: 400 });
    }

    // 3. Validate file type and size (increased for complete videos)
    const maxSize = 500 * 1024 * 1024; // 500MB for complete videos
    if (file.size > maxSize) {
      console.log('❌ File too large:', file.size, 'bytes');
      return NextResponse.json({ error: "File too large. Maximum size is 500MB." }, { status: 413 });
    }

    console.log('✅ File validation passed');

    // 4. Upload to Cloudinary
    const buffer = Buffer.from(await file.arrayBuffer());
    const timestamp = Date.now();
    const uniqueSuffix = `${timestamp}-${Math.round(Math.random() * 1e9)}`;
    
    console.log('🚀 Starting Cloudinary upload...');
    
    // Prepare upload options based on the incoming file type
    const uploadOptions = {
      resource_type: "video" as const,
      public_id: `videos/${userId}/session-${sessionId}/${uniqueSuffix}`,
      folder: "wildeye_recordings",
      quality: "auto:good",
      format: undefined as string | undefined,
      video_codec: undefined as string | undefined,
      audio_codec: undefined as string | undefined,
      fetch_format: undefined as string | undefined,
      transformation: [] as Array<Record<string, string>>,
    };

    // For WebM files, don't force conversion immediately - let Cloudinary handle it
    if (file.type?.includes('webm')) {
      console.log('📝 Handling WebM file format');
      // Upload as-is, then transform to MP4 if needed
      uploadOptions.fetch_format = "auto"; // Move fetch_format to top level
      uploadOptions.transformation = [
        { quality: "auto:good" }
      ];
    } else {
      // For other formats, convert to MP4
      uploadOptions.format = "mp4";
      uploadOptions.video_codec = "h264";
      uploadOptions.audio_codec = "aac";
      uploadOptions.fetch_format = "mp4"; // Move fetch_format to top level
      uploadOptions.transformation = [
        { quality: "auto:good" }
      ];
    }
    
    const cloudinaryResult = await uploadToCloudinary(buffer, uploadOptions);

    console.log('📝 Cloudinary result:', cloudinaryResult ? 'Success' : 'Failed');
    console.log('📝 Cloudinary URL:', cloudinaryResult?.secure_url);

    if (!cloudinaryResult || !cloudinaryResult.secure_url) {
      console.log('❌ Cloudinary upload failed');
      throw new Error("Cloudinary upload failed.");
    }

    console.log('✅ Cloudinary upload successful');

    // 5. Create database record for the video
    const title = uploadSession.user.name 
      ? `Recording by ${uploadSession.user.name}` 
      : `Mobile Recording`;

    console.log('🚀 Creating database record...');
    console.log('📝 Title:', title);
    console.log('📝 URL:', cloudinaryResult.secure_url);
    console.log('📝 User ID:', userId);
    console.log('📝 Session ID:', sessionId);
    
    // Parse and validate coordinates
    let parsedLatitude = null;
    let parsedLongitude = null;
    
    if (latitude && longitude && latitude.trim() !== '' && longitude.trim() !== '') {
      const latStr = latitude.trim();
      const lngStr = longitude.trim();
      
      parsedLatitude = parseFloat(latStr);
      parsedLongitude = parseFloat(lngStr);
      
      // Validate coordinates are reasonable
      if (isNaN(parsedLatitude) || isNaN(parsedLongitude)) {
        console.warn('⚠️ Invalid coordinates provided - NaN result:', latStr, lngStr);
        parsedLatitude = null;
        parsedLongitude = null;
      } else if (parsedLatitude < -90 || parsedLatitude > 90 || parsedLongitude < -180 || parsedLongitude > 180) {
        console.warn('⚠️ Coordinates out of range:', parsedLatitude, parsedLongitude);
        parsedLatitude = null;
        parsedLongitude = null;
      } else {
        console.log('✅ Valid coordinates parsed:', parsedLatitude, parsedLongitude);
      }
    } else {
      console.warn('⚠️ No coordinates provided in request or empty strings');
    }
    
    console.log('📝 Final coordinates for DB - Lat:', parsedLatitude, 'Lng:', parsedLongitude);
    
    // Create database record for the video with proper error handling
    console.log('🚀 Creating database record...');
    console.log('📝 Video data to save:', {
      title: title,
      url: cloudinaryResult.secure_url,
      userId: userId,
      uploadSessionId: sessionId,
      latitude: parsedLatitude,
      longitude: parsedLongitude,
    });
    
    let newVideo;
    try {
      newVideo = await db.video.create({
        data: {
          title: title,
          url: cloudinaryResult.secure_url,
          userId: userId,
          uploadSessionId: sessionId,
          latitude: parsedLatitude,
          longitude: parsedLongitude,
        },
      });
      
      console.log('✅ Database record created successfully');
      console.log('📝 Created video:', {
        id: newVideo.id,
        title: newVideo.title,
        latitude: newVideo.latitude,
        longitude: newVideo.longitude,
        url: newVideo.url
      });
      
    } catch (dbError) {
      console.error('❌ Database insertion failed:', dbError);
      // If DB fails, we should clean up the Cloudinary upload
      try {
        console.log('🧹 Attempting to cleanup Cloudinary upload...');
        await cloudinary.uploader.destroy(cloudinaryResult.public_id, { resource_type: 'video' });
        console.log('✅ Cloudinary cleanup successful');
      } catch (cleanupError) {
        console.error('❌ Cloudinary cleanup failed:', cleanupError);
      }
      
      throw new Error(`Database error: ${dbError instanceof Error ? dbError.message : 'Unknown database error'}`);
    }

    // 6. Mark session as complete after successful upload
    try {
      await db.uploadSession.update({
        where: { id: sessionId },
        data: { status: "COMPLETE" }
      });
      console.log('✅ Session marked as complete');
    } catch (sessionUpdateError) {
      console.error('⚠️ Failed to update session status:', sessionUpdateError);
      // Don't fail the whole request for this
    }
    
    console.log(`✅ Video uploaded successfully: ${newVideo.id}`);

    // 7. Return success response
    console.log('=== UPLOAD API DEBUG END ===');

    return NextResponse.json({
      ...newVideo,
      message: "Video uploaded successfully",
    }, { status: 201 });

  } catch (error) {
    console.error("💥 Upload API Error:", error);
    const errorMessage = error instanceof Error ? error.message : "Internal Server Error";
    console.log('=== UPLOAD API DEBUG END (ERROR) ===');
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
