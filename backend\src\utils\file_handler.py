"""
File handling utilities.
"""

import os
import shutil
from pathlib import Path
from typing import Binary<PERSON>

from ..config.settings import UPLOAD_DIR


class FileHandler:
    """Handles file operations for the application."""

    @staticmethod
    def ensure_directory_exists(directory_path: Path):
        """Ensure a directory exists, create if it doesn't."""
        os.makedirs(directory_path, exist_ok=True)

    @staticmethod
    def save_uploaded_file(file_content: BinaryIO, filename: str) -> str:
        """
        Save uploaded file to upload directory.

        Args:
            file_content: File content stream
            filename: Original filename

        Returns:
            Relative path to saved file
        """
        # Ensure upload directory exists
        FileHandler.ensure_directory_exists(UPLOAD_DIR)

        upload_file_path = UPLOAD_DIR / filename
        relative_path = f"/upload/{filename}"

        with open(upload_file_path, "wb") as f:
            shutil.copyfileobj(file_content, f)

        return relative_path

    @staticmethod
    def cleanup_uploaded_file(file_path: str):
        """
        Clean up uploaded file.

        Args:
            file_path: Path to file to clean up
        """
        try:
            full_path = UPLOAD_DIR.parent / file_path.lstrip("/")
            if full_path.exists():
                full_path.unlink()
                print(f"Cleaned up uploaded file: {file_path}")
        except Exception as e:
            print(f"Error cleaning up uploaded file {file_path}: {e}")


# Global file handler instance
file_handler = FileHandler()
