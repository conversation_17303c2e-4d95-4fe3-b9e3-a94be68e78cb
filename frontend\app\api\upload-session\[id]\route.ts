import { NextResponse } from "next/server";
import { db } from "@/lib/db";

export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const sessionId = (await params).id;
    const { action } = await request.json();

    if (action === "complete") {
      // Mark the session as complete so it can't be used again
      await db.uploadSession.update({
        where: { id: sessionId },
        data: { status: "COMPLETE" }
      });

      return NextResponse.json({ 
        message: "Session marked as complete" 
      });
    }

    return NextResponse.json({ 
      error: "Invalid action" 
    }, { status: 400 });

  } catch (error) {
    console.error("Session Update Error:", error);
    return NextResponse.json({ 
      error: "Internal Server Error" 
    }, { status: 500 });
  }
}
