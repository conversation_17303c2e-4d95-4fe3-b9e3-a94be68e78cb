@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(0.9969 0.005 145.538);
  --foreground: oklch(0.3415 0.0477 160.6308);
  --card: oklch(0.9928 0.0118 145.4864);
  --card-foreground: oklch(0.3415 0.0477 160.6308);
  --popover: oklch(0.9848 0.0252 145.3806);
  --popover-foreground: oklch(0.3415 0.0477 160.6308);
  --primary: oklch(0.7725 0.1686 152.7827);
  --primary-foreground: oklch(0.3415 0.0477 160.6308);
  --secondary: oklch(0.7294 0.1463 147.5193);
  --secondary-foreground: oklch(1 0 0);
  --muted: oklch(0.9476 0.0541 145.1329);
  --muted-foreground: oklch(0.5704 0.0441 162.9196);
  --accent: oklch(0.9695 0.0521 145.1594);
  --accent-foreground: oklch(0.3415 0.0477 160.6308);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.8992 0.0548 145.1023);
  --input: oklch(0.8992 0.0548 145.1023);
  --ring: oklch(0.9211 0.1405 148.5001);
  --chart-1: oklch(0.9211 0.1405 148.5001);
  --chart-2: oklch(0.7294 0.1463 147.5193);
  --chart-3: oklch(0.6294 0.1474 146.7711);
  --chart-4: oklch(0.5276 0.1457 145.6496);
  --chart-5: oklch(0.4286 0.1223 145.8436);
  --sidebar: oklch(0.9629 0.027 145.3614);
  --sidebar-foreground: oklch(0.3415 0.0477 160.6308);
  --sidebar-primary: oklch(0.9211 0.1405 148.5001);
  --sidebar-primary-foreground: oklch(0.3415 0.0477 160.6308);
  --sidebar-accent: oklch(0.9695 0.0521 145.1594);
  --sidebar-accent-foreground: oklch(0.3415 0.0477 160.6308);
  --sidebar-border: oklch(0.8992 0.0548 145.1023);
  --sidebar-ring: oklch(0.9211 0.1405 148.5001);
  --font-sans: Inter;
  --font-serif: Bebas Neue;
  --font-mono: Fira Code;
  --radius: 0.5rem;
  --shadow-2xs: 0px 0.125rem 0.25rem 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 0.125rem 0.25rem 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 0.125rem 0.25rem 0px hsl(0 0% 0% / 0.1),
    0px 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow: 0px 0.125rem 0.25rem 0px hsl(0 0% 0% / 0.1),
    0px 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md: 0px 0.125rem 0.25rem 0px hsl(0 0% 0% / 0.1),
    0px 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg: 0px 0.125rem 0.25rem 0px hsl(0 0% 0% / 0.1),
    0px 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl: 0px 0.125rem 0.25rem 0px hsl(0 0% 0% / 0.1),
    0px 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0px 0.125rem 0.25rem 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.1729 0.0149 153.57);
  --foreground: oklch(0.9695 0.0521 145.1594);
  --card: oklch(0.2065 0.0178 155.3653);
  --card-foreground: oklch(0.9695 0.0521 145.1594);
  --popover: oklch(0.2701 0.0306 155.4185);
  --popover-foreground: oklch(0.9695 0.0521 145.1594);
  --primary: oklch(0.9211 0.1405 148.5001);
  --primary-foreground: oklch(0.2701 0.0306 155.4185);
  --secondary: oklch(0.6294 0.1474 146.7711);
  --secondary-foreground: oklch(1 0 0);
  --muted: oklch(0.3306 0.0285 154.5649);
  --muted-foreground: oklch(0.7929 0.0442 150.4206);
  --accent: oklch(0.3415 0.0477 160.6308);
  --accent-foreground: oklch(0.9695 0.0521 145.1594);
  --destructive: oklch(0.7116 0.1812 22.8389);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.311 0.0289 154.4105);
  --input: oklch(0.311 0.0289 154.4105);
  --ring: oklch(0.9211 0.1405 148.5001);
  --chart-1: oklch(0.9211 0.1405 148.5001);
  --chart-2: oklch(0.7294 0.1463 147.5193);
  --chart-3: oklch(0.6294 0.1474 146.7711);
  --chart-4: oklch(0.5276 0.1457 145.6496);
  --chart-5: oklch(0.4286 0.1223 145.8436);
  --sidebar: oklch(0.2462 0.0302 153.6775);
  --sidebar-foreground: oklch(0.9695 0.0521 145.1594);
  --sidebar-primary: oklch(0.9211 0.1405 148.5001);
  --sidebar-primary-foreground: oklch(0.2701 0.0306 155.4185);
  --sidebar-accent: oklch(0.3415 0.0477 160.6308);
  --sidebar-accent-foreground: oklch(0.9695 0.0521 145.1594);
  --sidebar-border: oklch(0.311 0.0289 154.4105);
  --sidebar-ring: oklch(0.9211 0.1405 148.5001);
  --font-sans: Inter;
  --font-serif: Bebas Neue;
  --font-mono: Fira Code;
  --radius: 0.5rem;
  --shadow-2xs: 0px 0.125rem 0.25rem 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 0.125rem 0.25rem 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 0.125rem 0.25rem 0px hsl(0 0% 0% / 0.1),
    0px 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow: 0px 0.125rem 0.25rem 0px hsl(0 0% 0% / 0.1),
    0px 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md: 0px 0.125rem 0.25rem 0px hsl(0 0% 0% / 0.1),
    0px 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg: 0px 0.125rem 0.25rem 0px hsl(0 0% 0% / 0.1),
    0px 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl: 0px 0.125rem 0.25rem 0px hsl(0 0% 0% / 0.1),
    0px 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0px 0.125rem 0.25rem 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.stroked-text {
  text-shadow: 2px 2px 0 #000;
  -webkit-text-stroke: 2px #000;
}
