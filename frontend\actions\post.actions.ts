"use server";

import { db } from "@/lib/db";
import { revalidatePath } from "next/cache";

export async function createPost(formData: FormData) {
  const title = formData.get("title") as string;
  const content = formData.get("content") as string;
  const userId = formData.get("userId") as string;

  if (!title || !content || !userId) {
    throw new Error("Title, content, and user ID are required");
  }

  try {
    const post = await db.post.create({
      data: {
        title,
        content,
        userId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    revalidatePath("/community");
    return { success: true, post };
  } catch (error) {
    console.error("Error creating post:", error);
    return { success: false, error: "Failed to create post" };
  }
}

export async function getPosts() {
  try {
    const posts = await db.post.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return { success: true, posts };
  } catch (error) {
    console.error("Error fetching posts:", error);
    return { success: false, error: "Failed to fetch posts" };
  }
}

export async function likePost(postId: string) {
  try {
    const post = await db.post.update({
      where: { id: postId },
      data: {
        likes: {
          increment: 1,
        },
      },
    });

    revalidatePath("/community");
    return { success: true, post };
  } catch (error) {
    console.error("Error liking post:", error);
    return { success: false, error: "Failed to like post" };
  }
}

export async function deletePost(postId: string, userId: string) {
  try {
    // First check if the user owns the post
    const post = await db.post.findUnique({
      where: { id: postId },
      select: { userId: true },
    });

    if (!post) {
      return { success: false, error: "Post not found" };
    }

    if (post.userId !== userId) {
      return { success: false, error: "Unauthorized to delete this post" };
    }

    await db.post.delete({
      where: { id: postId },
    });

    revalidatePath("/community");
    return { success: true };
  } catch (error) {
    console.error("Error deleting post:", error);
    return { success: false, error: "Failed to delete post" };
  }
}
