"""
Location generation service for wildlife detections.
"""

import random
from math import radians, sin, cos, sqrt
from typing import List, Dict, Any

from ..config.settings import DEFAULT_LATITUDE, DEFAULT_LONGITUDE, MAX_DISTANCE_KM


class LocationService:
    """Handles location generation for detections."""

    @staticmethod
    def generate_random_neighbor(
        lat: float, lon: float, max_distance_km: float
    ) -> tuple:
        """
        Generate a random neighboring location within a given range.

        Args:
            lat: Base latitude
            lon: Base longitude
            max_distance_km: Maximum distance in kilometers

        Returns:
            tuple: (new_latitude, new_longitude)
        """
        earth_radius_km = 6371.0

        lat_diff = max_distance_km / earth_radius_km
        lon_diff = max_distance_km / (earth_radius_km * cos(radians(lat)))

        new_lat = lat + random.uniform(-lat_diff, lat_diff)
        new_lon = lon + random.uniform(-lon_diff, lon_diff)

        return new_lat, new_lon

    def add_random_locations(
        self,
        data: List[Dict[str, Any]],
        given_lat: float = None,
        given_lon: float = None,
        max_distance_km: float = None,
    ) -> List[Dict[str, Any]]:
        """
        Add random locations to detection data.

        Args:
            data: List of frame detection data
            given_lat: Base latitude (defaults to config value)
            given_lon: Base longitude (defaults to config value)
            max_distance_km: Maximum distance in km (defaults to config value)

        Returns:
            Updated data with location information
        """
        if given_lat is None:
            given_lat = DEFAULT_LATITUDE
        if given_lon is None:
            given_lon = DEFAULT_LONGITUDE
        if max_distance_km is None:
            max_distance_km = MAX_DISTANCE_KM

        for frame in data:
            for detection in frame["detections"]:
                num_locations = random.randint(
                    1, 3
                )  # Generate 1 to 3 random neighboring locations

                for _ in range(num_locations):
                    new_lat, new_lon = self.generate_random_neighbor(
                        given_lat, given_lon, max_distance_km
                    )

                    # Append new location to the detection
                    detection["latitude"] = new_lat
                    detection["longitude"] = new_lon

        return data


# Global location service instance
location_service = LocationService()
