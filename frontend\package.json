{"name": "frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "db:generate": "prisma generate --no-hint", "db:push": "prisma db push", "db:studio": "prisma studio", "postinstall": "prisma generate"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@prisma/client": "^6.14.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@tabler/icons-react": "^3.34.1", "@tanstack/react-table": "^8.21.3", "@types/uuid": "^10.0.0", "@yudiel/react-qr-scanner": "^2.3.1", "class-variance-authority": "^0.7.1", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "lucide-react": "^0.541.0", "next": "15.5.0", "next-auth": "^5.0.0-beta.29", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-qr-code": "^2.0.18", "recharts": "2.15.4", "resend": "^6.0.1", "sonner": "^2.0.7", "swr": "^2.3.6", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^4.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.0", "prisma": "^6.14.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.7", "typescript": "^5"}}