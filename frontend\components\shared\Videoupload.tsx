"use client";

import VideoRecorder from "@/components/shared/video-recorder";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState, useEffect } from "react";
import { CheckCircle, Loader, Upload, XCircle, MapPin } from "lucide-react";
import { Button } from "@/components/ui/button";

export default function Videoupload({ sessionId }: { sessionId: string }) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const [sessionExpired, setSessionExpired] = useState(false);
  const [recordingStarted, setRecordingStarted] = useState(false);
  const [userLocation, setUserLocation] = useState<GeolocationCoordinates | null>(null);
  const [locationPermission, setLocationPermission] = useState<'pending' | 'granted' | 'denied' | 'error'>('pending');
  const [locationError, setLocationError] = useState<string | null>(null);

  // Request location permission when component mounts
  useEffect(() => {
    requestLocationPermission();
  }, []);

  const requestLocationPermission = () => {
    if (!navigator.geolocation) {
      setLocationPermission('error');
      setLocationError('Geolocation is not supported by this browser');
      console.error("❌ Geolocation not supported by browser");
      return;
    }

    console.log('🌍 Requesting user location permission...');
    setLocationPermission('pending');
    
    navigator.geolocation.getCurrentPosition(
      (position) => {
        setUserLocation(position.coords);
        setLocationPermission('granted');
        setLocationError(null);
        console.log("✅ Location permission granted and acquired:", position.coords.latitude, position.coords.longitude);
        console.log("📍 Accuracy:", position.coords.accuracy, "meters");
      },
      (error) => {
        setLocationPermission('denied');
        console.error("❌ Error getting location:", error.message);
        console.error("❌ Error code:", error.code);
        
        switch(error.code) {
          case error.PERMISSION_DENIED:
            setLocationError("Location access was denied. Please enable location access in your browser settings to tag your recordings with location data.");
            break;
          case error.POSITION_UNAVAILABLE:
            setLocationError("Location information is unavailable.");
            break;
          case error.TIMEOUT:
            setLocationError("Location request timed out. Please try again.");
            break;
          default:
            setLocationError("An unknown error occurred while retrieving location.");
            break;
        }
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 60000
      }
    );
  };

  const uploadVideo = async (videoFile: Blob | File, fileName: string, coords: GeolocationCoordinates | null) => {
    setIsUploading(true);
    setUploadError(null);
    setUploadSuccess(false);

    const formData = new FormData();
    formData.append("video", videoFile, fileName);
    
    // Use provided coords or fallback to stored location
    const locationToUse = coords || userLocation;
    if (locationToUse) {
      formData.append("latitude", locationToUse.latitude.toString());
      formData.append("longitude", locationToUse.longitude.toString());
    }

    try {
      console.log("🚀 Uploading complete video to:", `/api/upload/${sessionId}`);
      
      const response = await fetch(`/api/upload/${sessionId}`, {
        method: "POST",
        body: formData,
      });

      console.log("📊 Upload response status:", response.status);
      
      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 410) {
          setSessionExpired(true);
          throw new Error(errorData.error || "Upload session has expired.");
        }
        throw new Error(errorData.error || "Upload failed.");
      }

      const result = await response.json();
      console.log("✅ Video uploaded successfully:", result);
      
      // The API automatically marks the session as COMPLETE after successful upload
      // Now we can show the success state
      setUploadSuccess(true);
      
    } catch (error) {
      if (error instanceof Error) {
        setUploadError(error.message);
      } else {
        setUploadError("An unknown error occurred during upload.");
      }
    } finally {
      setIsUploading(false);
    }
  };

  const uploadVideoFromRecorder = async (video: Blob, coords: GeolocationCoordinates | null) => {
    console.log('=== UPLOAD VIDEO DEBUG ===');
    console.log('Video size:', video.size, 'bytes');
    console.log('Video type:', video.type);
    console.log('Session ID:', sessionId);
    console.log('Coords provided from recorder:', coords);
    console.log('User location backup:', userLocation);
    
    if (!recordingStarted) {
      setRecordingStarted(true);
    }

    // Create a proper filename with appropriate extension
    const extension = video.type.includes('webm') ? 'webm' : 'mp4';
    const filename = `recording-${Date.now()}.${extension}`;
    
    try {
      await uploadVideo(video, filename, coords);
      console.log("✅ Video uploaded successfully");
    } catch (error) {
      console.error("💥 Video upload error:", error);
      throw error; // Re-throw to be handled by video recorder
    }
    console.log('=== END UPLOAD VIDEO DEBUG ===');
  };

  const handleRecordingComplete = () => {
    console.log('🎬 Recording completed, waiting for upload to finish');
    // Don't expire session here - let the upload API handle it after successful upload
    // This prevents the session from being expired before the video is actually uploaded
  };

  const handleManualUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      uploadVideo(file, file.name, userLocation);
    }
  };
  
  const resetState = () => {
    setUploadError(null);
    setSessionExpired(false);
  };

  if (sessionExpired) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background dark:bg-gray-900">
        <Card className="w-full max-w-md text-center p-8 shadow-2xl">
          <CardHeader>
            <CheckCircle className="mx-auto h-16 w-16 text-green-500" />
            <CardTitle className="text-3xl font-bold mt-4">Recording Complete!</CardTitle>
            <CardDescription className="pt-2 text-lg">
              Your video recording session has been completed and securely closed. All video chunks have been successfully uploaded to the cloud.
            </CardDescription>
            <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <p className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                🔒 For security, this session link has been expired and cannot be used again.
              </p>
              <p className="text-xs text-blue-500 dark:text-blue-300 mt-2">
                To record again, please generate a new QR code from the dashboard.
              </p>
            </div>
          </CardHeader>
        </Card>
      </div>
    );
  }

  if (uploadSuccess) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background dark:bg-gray-900">
        <Card className="w-full max-w-md text-center p-8 shadow-2xl">
          <CardHeader>
            <CheckCircle className="mx-auto h-16 w-16 text-green-500" />
            <CardTitle className="text-3xl font-bold mt-4">Recording Complete!</CardTitle>
            <CardDescription className="pt-2 text-lg">
              Your video recording has been uploaded successfully. You can now close this window.
            </CardDescription>
            {userLocation && (
              <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="flex items-center justify-center gap-2 text-blue-600 dark:text-blue-400">
                  <MapPin className="w-4 h-4" />
                  <span className="text-sm font-medium">Location Recorded</span>
                </div>
                <p className="text-xs text-blue-500 dark:text-blue-500 mt-1 font-mono">
                  {userLocation.latitude.toFixed(6)}, {userLocation.longitude.toFixed(6)}
                </p>
              </div>
            )}
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10 px-4">
      <Card className="max-w-4xl mx-auto shadow-xl overflow-hidden">
        <CardHeader className="text-center bg-gray-50 dark:bg-gray-800/50 p-6">
          <CardTitle className="text-3xl font-bold">Record Your Video</CardTitle>
          <CardDescription>
            Record your complete video in one take. When you stop recording, it will automatically upload to the cloud.
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6 sm:p-8 space-y-8">
          {/* Location Permission Card */}
          {locationPermission === 'pending' && (
            <div className="p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-center gap-3 text-blue-700 dark:text-blue-300">
                <MapPin className="w-6 h-6 animate-pulse" />
                <div>
                  <h3 className="font-semibold text-lg">Requesting Location Access</h3>
                  <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
                    Please allow location access to tag your recordings with location data.
                  </p>
                </div>
              </div>
            </div>
          )}

          {locationPermission === 'denied' && (
            <div className="p-6 bg-amber-50 dark:bg-amber-900/20 rounded-lg border border-amber-200 dark:border-amber-800">
              <div className="flex items-center gap-3 text-amber-700 dark:text-amber-300">
                <XCircle className="w-6 h-6" />
                <div className="flex-1">
                  <h3 className="font-semibold text-lg">Location Access Denied</h3>
                  <p className="text-sm text-amber-600 dark:text-amber-400 mt-1">
                    {locationError || "Location access was denied. Your recordings will be saved without location data."}
                  </p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={requestLocationPermission}
                  className="text-amber-700 border-amber-300 hover:bg-amber-100 dark:text-amber-300 dark:border-amber-700 dark:hover:bg-amber-900/40"
                >
                  Try Again
                </Button>
              </div>
            </div>
          )}

          {locationPermission === 'error' && (
            <div className="p-6 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
              <div className="flex items-center gap-3 text-red-700 dark:text-red-300">
                <XCircle className="w-6 h-6" />
                <div>
                  <h3 className="font-semibold text-lg">Location Not Available</h3>
                  <p className="text-sm text-red-600 dark:text-red-400 mt-1">
                    {locationError || "Location services are not available in this browser."}
                  </p>
                </div>
              </div>
            </div>
          )}

          {locationPermission === 'granted' && userLocation && (
            <div className="p-6 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
              <div className="flex items-center gap-3 text-green-700 dark:text-green-300">
                <CheckCircle className="w-6 h-6" />
                <div>
                  <h3 className="font-semibold text-lg">Location Ready</h3>
                  <p className="text-sm text-green-600 dark:text-green-400 mt-1">
                    Your recordings will be tagged with location: {userLocation.latitude.toFixed(4)}, {userLocation.longitude.toFixed(4)}
                  </p>
                  <p className="text-xs text-green-500 dark:text-green-500 mt-1">
                    Accuracy: ±{userLocation.accuracy?.toFixed(0) || 'Unknown'} meters
                  </p>
                </div>
              </div>
            </div>
          )}

          {isUploading ? (
            <div className="flex flex-col items-center justify-center gap-4 p-8">
              <Loader className="h-12 w-12 animate-spin text-primary" />
              <p className="text-lg font-semibold">Uploading, please wait...</p>
              <p className="text-sm text-muted-foreground">This may take a moment.</p>
            </div>
          ) : uploadError ? (
            <div className="text-center p-8 bg-red-50 dark:bg-red-900/20 rounded-lg">
              <XCircle className="mx-auto h-12 w-12 text-red-500" />
              <h2 className="text-2xl font-bold mt-4">Upload Failed</h2>
              <p className="mt-2 text-red-600 dark:text-red-400">{uploadError}</p>
              <Button onClick={resetState} className="mt-6">Try Again</Button>
            </div>
          ) : (
            <>
              <VideoRecorder 
                onRecordingComplete={uploadVideoFromRecorder}
                onRecordingStop={handleRecordingComplete}
              />
              
              <div className="relative my-8">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="bg-card px-3 text-muted-foreground uppercase">Or</span>
                </div>
              </div>

              <div>
                <Label htmlFor="manual-upload" className="sr-only">Upload a file</Label>
                <label 
                  htmlFor="manual-upload" 
                  className="w-full cursor-pointer border-2 border-dashed rounded-lg p-8 flex flex-col items-center justify-center text-center hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
                >
                  <Upload className="h-10 w-10 text-muted-foreground mb-3" />
                  <span className="font-semibold text-lg">Click to upload a file</span>
                  <span className="text-sm text-muted-foreground mt-1">MP4, MOV, or WEBM (Max 100MB)</span>
                  <Input id="manual-upload" type="file" accept="video/*" onChange={handleManualUpload} className="hidden" />
                </label>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
