<!DOCTYPE html>
<html>

<head>
  <title>Video Test</title>
</head>

<body>
  <h1>Video Test</h1>
  <h2>Browser Optimized Version (H.264)</h2>
  <video controls width='640' height='360' preload='metadata' id='testvideo'>
    <source src='http://localhost:8000/static/out/35392f9a-62e0-44cf-a3d6-38c88fe0e66d_browser_optimized.mp4'
      type='video/mp4'>
    Your browser does not support the video tag.
  </video>
  <p><a href='http://localhost:8000/static/out/35392f9a-62e0-44cf-a3d6-38c88fe0e66d_browser_optimized.mp4'>Direct Link
      (Optimized)</a></p>

  <h2>Original Version (MPEG-4)</h2>
  <video controls width='640' height='360' preload='metadata' id='testvideo2'>
    <source src='http://localhost:8000/static/out/35392f9a-62e0-44cf-a3d6-38c88fe0e66d_processed.mp4' type='video/mp4'>
    Your browser does not support the video tag.
  </video>
  <p><a href='http://localhost:8000/static/out/35392f9a-62e0-44cf-a3d6-38c88fe0e66d_processed.mp4'>Direct Link
      (Original)</a></p>

  <div id='debug'></div>
  <script>
    const video = document.getElementById('testvideo');
    const video2 = document.getElementById('testvideo2');
    const debug = document.getElementById('debug');

    function addDebug(msg) {
      debug.innerHTML += new Date().toLocaleTimeString() + ': ' + msg + '<br>';
    }

    video.addEventListener('loadstart', () => addDebug('Optimized - loadstart'));
    video.addEventListener('durationchange', () => addDebug('Optimized - durationchange'));
    video.addEventListener('loadedmetadata', () => addDebug('Optimized - loadedmetadata'));
    video.addEventListener('loadeddata', () => addDebug('Optimized - loadeddata'));
    video.addEventListener('canplay', () => addDebug('Optimized - canplay'));
    video.addEventListener('canplaythrough', () => addDebug('Optimized - canplaythrough'));
    video.addEventListener('error', (e) => addDebug('Optimized - error: ' + e.target.error.message));

    video2.addEventListener('loadstart', () => addDebug('Original - loadstart'));
    video2.addEventListener('durationchange', () => addDebug('Original - durationchange'));
    video2.addEventListener('loadedmetadata', () => addDebug('Original - loadedmetadata'));
    video2.addEventListener('loadeddata', () => addDebug('Original - loadeddata'));
    video2.addEventListener('canplay', () => addDebug('Original - canplay'));
    video2.addEventListener('canplaythrough', () => addDebug('Original - canplaythrough'));
    video2.addEventListener('error', (e) => addDebug('Original - error: ' + e.target.error.message));
  </script>
</body>

</html>