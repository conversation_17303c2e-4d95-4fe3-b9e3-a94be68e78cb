import { NextResponse } from "next/server";
import { db } from "@/lib/db";

export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const sessionId = (await params).id;
    console.log('🔒 Expiring session:', sessionId);

    // Mark the session as expired
    const updatedSession = await db.uploadSession.update({
      where: { id: sessionId },
      data: { status: "EXPIRED" }
    });

    console.log('✅ Session expired successfully:', updatedSession.id);

    return NextResponse.json({
      message: "Upload session expired successfully",
      sessionId: updatedSession.id
    }, { status: 200 });

  } catch (error) {
    console.error("💥 Error expiring session:", error);
    const errorMessage = error instanceof Error ? error.message : "Internal Server Error";
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
