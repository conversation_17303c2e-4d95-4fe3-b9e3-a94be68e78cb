from src.services.location_service import location_service
from src.services.data_service import data_service
from src.services.video_processor import video_processor
from src.services.notification_service import notification_service
from src.core.model_manager import model_manager
from fastapi import FastAPI, UploadFile, File, Form, Request
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
import os
import shutil
import uuid
from scalar_fastapi.scalar_fastapi import get_scalar_api_reference
import asyncio
import time


app = FastAPI(
    title="Wildlife Anomaly Detection API",
    description="API for wildlife detection and anomaly analysis using YOLOv5",
    version="1.0.0",
    docs_url=None,
    redoc_url=None,
)

# Add CORS middleware to allow browser access to static files
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify your frontend domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

given_lat = 22.606803
given_lon = 85.338173

max_distance_km = 20


@app.on_event("startup")
async def startup_event():
    """Load models on startup to avoid loading them on each request."""
    print("🚀 Starting Wildlife Anomaly Detection API...")
    print("📦 Pre-loading YOLO models at startup...")

    start_time = time.time()

    try:
        # Load models in a separate thread to avoid blocking startup
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, _preload_models)

        load_time = time.time() - start_time
        print(f"✅ Models loaded successfully in {load_time:.2f} seconds")
        print(f"🎯 Device: {model_manager.device}")

    except Exception as e:
        print(f"❌ Error loading models at startup: {e}")
        print("⚠️ Models will be loaded on first request instead")


def _preload_models():
    """Helper function to preload models synchronously."""
    try:
        # Force load both models
        day_model, thermal_model = model_manager.load_models()

        # Force video processor to use the preloaded models
        video_processor.day_model = day_model
        video_processor.thermal_model = thermal_model

        print("📋 Model preloading complete:")
        print(f"  - Day model loaded: {day_model is not None}")
        print(f"  - Thermal model loaded: {thermal_model is not None}")

    except Exception as e:
        print(f"❌ Error in model preloading: {e}")
        raise


@app.on_event("shutdown")
async def shutdown_event():
    """Clean up resources on shutdown."""
    print("🛑 Shutting down Wildlife Anomaly Detection API...")
    print("🧹 Cleaning up resources...")


@app.get("/")
async def root():
    """Root endpoint returning API information"""
    return JSONResponse(
        content={
            "message": "Wildlife Anomaly Detection API",
            "version": "1.0.0",
            "endpoints": {
                "/upload/": "POST - Upload video for wildlife detection",
                "/sample-data/": "GET - Get sample detection data",
                "/health": "GET - Health check endpoint",
            },
        }
    )


@app.get("/health")
async def health_check():
    """Health check endpoint with model status"""
    # Check if models are loaded
    day_model_loaded = video_processor.day_model is not None
    thermal_model_loaded = video_processor.thermal_model is not None

    return JSONResponse(
        content={
            "status": "healthy",
            "service": "wildlife-detection-api",
            "models": {
                "day_model_loaded": day_model_loaded,
                "thermal_model_loaded": thermal_model_loaded,
                "device": model_manager.device,
            },
        }
    )


@app.post("/upload/")
async def predict_video(
    request: Request,
    video: UploadFile = File(...),
    selected_class: str = Form(...),
    getAlert: bool = Form(False),
):
    """
    Upload and process video for wildlife detection.

    Args:
        video: Uploaded video file
        selected_class: Video class ("Daylight" or "Thermal")
        getAlert: Whether to send alerts for critical detections

    Returns:
        JSON response with processing results including:
        - video_data_uri: Path to uploaded video
        - predicted_video_url: Path to processed video with detections
        - results: Detection results with location data
    """
    try:
        # Validate video file
        if not video.filename:
            return JSONResponse(
                status_code=400,
                content={
                    "status": "error",
                    "message": "No filename provided",
                    "data": None,
                },
            )

        # Check file extension
        allowed_extensions = {".mp4", ".avi", ".mov", ".webm", ".mkv"}
        file_extension = ""
        if "." in video.filename:
            file_extension = video.filename.rsplit(".", 1)[1].lower()
            if f".{file_extension}" not in allowed_extensions:
                return JSONResponse(
                    status_code=400,
                    content={
                        "status": "error",
                        "message": f"Unsupported video format: {file_extension}. Allowed: {', '.join(allowed_extensions)}",
                        "data": None,
                    },
                )
        else:
            file_extension = "mp4"  # default extension

        # Generate UUID for this video processing session
        video_uuid = str(uuid.uuid4())
        print(f"🆔 Generated video UUID: {video_uuid}")

        # Create paths with UUID
        upload_filename = f"{video_uuid}.{file_extension}"
        upload_video_path = f"/upload/{upload_filename}"

        # Ensure upload directory exists
        os.makedirs("static/upload", exist_ok=True)
        print(f"📁 Saving video to: {upload_video_path}")

        # Save uploaded video to upload directory
        with open(f"static{upload_video_path}", "wb") as f:
            shutil.copyfileobj(video.file, f)
        # Process video for wildlife detection
        print(f"🎬 Processing video with UUID: {video_uuid}")
        print(f"📁 Input: {upload_video_path}")

        result, predicted_video_path = video_processor.process_video(
            upload_video_path, selected_class, video_uuid
        )

        print(f"✅ Video processing completed!")
        print(f"📁 Output: {predicted_video_path}")

        # Build full URLs for returned resources (served under /static)
        base = str(request.base_url).rstrip("/")
        full_upload_url = f"{base}/static{upload_video_path}"
        full_predicted_url = f"{base}/static{predicted_video_path}"

        # Add random location data to results
        updated_result = location_service.add_random_locations(
            result, given_lat, given_lon, max_distance_km
        )

        print(f"Alert requested: {getAlert}")

        # Get alert information
        alert_info = notification_service.process_alerts(updated_result)

        return JSONResponse(
            content={
                "status": "success",
                "message": "Video processed successfully",
                "data": {
                    "video_uuid": video_uuid,
                    "video_data_uri": full_upload_url,
                    "predicted_video_url": full_predicted_url,
                    "results": updated_result,
                    "alert_classes": alert_info["alert_classes"],
                    "primary_alert_class": alert_info["primary_alert_class"],
                    "is_poaching_detected": alert_info["is_poaching_detected"],
                    # Keep backward compatibility
                    "alert_class": alert_info["alert_class"],
                },
            }
        )

    except FileNotFoundError as e:
        return JSONResponse(
            status_code=404,
            content={
                "status": "error",
                "message": "Video file not found or could not be processed",
                "data": {
                    "video_uuid": video_uuid if "video_uuid" in locals() else None
                },
            },
        )
    except PermissionError as e:
        return JSONResponse(
            status_code=403,
            content={
                "status": "error",
                "message": "Permission denied while processing video",
                "data": {
                    "video_uuid": video_uuid if "video_uuid" in locals() else None
                },
            },
        )
    except Exception as e:
        print(f"❌ Error processing video: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"Error processing video: {str(e)}",
                "data": {
                    "video_uuid": video_uuid if "video_uuid" in locals() else None
                },
            },
        )


@app.get("/sample-data/")
async def get_sample_data():
    """
    Get sample detection data for testing.

    Returns:
        JSON response with sample wildlife detection data including alert information
    """
    try:
        sample_data = data_service.load_sample_data()
        updated_data = location_service.add_random_locations(
            sample_data, given_lat, given_lon, max_distance_km
        )

        # Get alert information for sample data
        alert_info = notification_service.process_alerts(updated_data)

        return JSONResponse(
            content={
                "status": "success",
                "message": "Sample data retrieved successfully",
                "data": {
                    "results": updated_data,
                    "alert_classes": alert_info["alert_classes"],
                    "primary_alert_class": alert_info["primary_alert_class"],
                    "is_poaching_detected": alert_info["is_poaching_detected"],
                    # Keep backward compatibility
                    "alert_class": alert_info["alert_class"],
                },
            }
        )

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"Error retrieving sample data: {str(e)}",
                "data": None,
            },
        )


@app.get("/docs", include_in_schema=False)
async def scalar_html():
    return get_scalar_api_reference(
        openapi_url=app.openapi_url,
        title=app.title,
    )


# Mount static files - this should be at the end
app.mount("/static", StaticFiles(directory="static"), name="static")
