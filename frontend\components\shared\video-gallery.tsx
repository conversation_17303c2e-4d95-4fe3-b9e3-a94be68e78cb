"use client";

import use<PERSON><PERSON> from "swr";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { MapPin, Calendar, Clock } from "lucide-react";

const fetcher = (url: string) => fetch(url).then((res) => res.json());

interface Video {
  id: string;
  title: string;
  url: string;
  createdAt: string;
  latitude: number | null;
  longitude: number | null;
  user: {
    name: string | null;
    image: string | null;
  };
}

export default function VideoGallery({ sessionId }: { sessionId: string }) {
  console.log('VideoGallery rendered with sessionId:', sessionId);
  
  const { data: videos, error, isLoading } = useSWR<Video[]>(
    `/api/videos?sessionId=${sessionId}`,
    fetcher,
    {
      revalidateOnFocus: true,
      refreshInterval: 3000, // 3 seconds for faster updates
      revalidateOnMount: true,
      revalidateOnReconnect: true,
    }
  );

  console.log('VideoGallery state:', { 
    videosCount: videos?.length || 0, 
    error: !!error, 
    isLoading 
  });

  if (videos) {
    console.log('Videos in gallery:', videos.map(v => ({
      id: v.id,
      title: v.title,
      hasLocation: !!(v.latitude && v.longitude)
    })));
  }

  const formatDuration = (createdAt: string) => {
    const date = new Date(createdAt);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return "Just now";
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i} className="overflow-hidden">
            <Skeleton className="h-48 w-full" />
            <CardHeader className="pb-2">
              <Skeleton className="h-5 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent className="pb-2">
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-2/3" />
            </CardContent>
            <CardFooter className="flex items-center gap-3 pt-2">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-3 w-16" />
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-500 text-lg font-medium">Failed to load videos</p>
        <p className="text-gray-500 mt-2">Please try again later or refresh the page</p>
      </div>
    );
  }

  if (!videos || videos.length === 0) {
    return (
      <div className="text-center">
        <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
          <MapPin className="w-8 h-8 text-gray-400" />
        </div>
        <p className="text-lg font-medium text-gray-700 dark:text-gray-300">No videos recorded yet</p>
        <p className="text-gray-500 dark:text-gray-400 mt-2">
          Scan the QR code with your mobile device to start recording
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
      {videos.map((video) => (
        <Card 
          key={video.id} 
          className="overflow-hidden group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 bg-gradient-to-b from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 border-0 shadow-lg py-0!"
        >
          <CardHeader className="p-0 relative">
            <div className="relative overflow-hidden rounded-t-lg">
              <video 
                src={video.url} 
                controls 
                muted 
                className="w-full h-48 object-cover bg-black transition-transform duration-300 group-hover:scale-105"
                poster=""
              />
              <div className="absolute top-2 right-2">
                <Badge variant="secondary" className="bg-black/50 text-white border-0">
                  <Clock className="w-3 h-3 mr-1" />
                  {formatDuration(video.createdAt)}
                </Badge>
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="p-4 space-y-3">
            <CardTitle className="text-lg font-bold leading-tight text-gray-800 dark:text-gray-200 line-clamp-2">
              {video.title}
            </CardTitle>
            
            {video.latitude && video.longitude && (
              <div className="relative p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800 overflow-hidden">
                <div className="absolute top-0 right-0 w-20 h-20 bg-blue-100 dark:bg-blue-800/30 rounded-full -translate-y-10 translate-x-10 opacity-50"></div>
                <div className="relative">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="p-1 bg-blue-500 rounded-full">
                      <MapPin className="w-3 h-3 text-white" />
                    </div>
                    <span className="font-bold text-blue-800 dark:text-blue-300 text-sm">Location Recorded</span>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">Lat:</span>
                      <span className="text-xs font-mono text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-800/30 px-2 py-0.5 rounded">
                        {video.latitude.toFixed(6)}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">Lng:</span>
                      <span className="text-xs font-mono text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-800/30 px-2 py-0.5 rounded">
                        {video.longitude.toFixed(6)}
                      </span>
                    </div>
                  </div>
                  <button 
                    onClick={() => window.open(`https://maps.google.com/?q=${video.latitude},${video.longitude}`, '_blank')}
                    className="mt-3 w-full flex items-center justify-center gap-2 bg-blue-500 hover:bg-blue-600 text-white text-xs font-medium py-2 px-3 rounded-lg transition-colors"
                  >
                    <span>�️</span>
                    <span>View on Google Maps</span>
                  </button>
                </div>
              </div>
            )}
            
            <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
              <Calendar className="w-3 h-3" />
              <span>{new Date(video.createdAt).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
              })}</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
