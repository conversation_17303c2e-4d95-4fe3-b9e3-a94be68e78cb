"""
API routes for wildlife detection system.
"""

from fastapi import APIRouter, UploadFile, File, Form, Request
from fastapi.responses import JSONResponse
from typing import List, Dict, Any

from ..services.video_processor import video_processor
from ..services.location_service import location_service
from ..services.notification_service import notification_service
from ..services.data_service import data_service
from ..utils.file_handler import file_handler
from ..config.settings import DEFAULT_LATITUDE, DEFAULT_LONGITUDE, MAX_DISTANCE_KM

# Create API router
api_router = APIRouter()


@api_router.post("/upload/")
async def predict_video(
    request: Request,
    video: UploadFile = File(...),
    selected_class: str = Form(...),
    getAlert: bool = Form(False),
):
    """
    Upload and process video for wildlife detection.

    Args:
        video: Uploaded video file
        selected_class: Video class ("Daylight" or "Thermal")
        getAlert: Whether to send alerts for critical detections

    Returns:
        JSON response with processing results
    """
    try:
        # Save uploaded file
        temp_video_path = file_handler.save_uploaded_file(video.file, video.filename)

        # Process video
        result, predicted_video_path = video_processor.process_video(
            temp_video_path, selected_class
        )

        # Build full URLs (served under /static)
        base = str(request.base_url).rstrip("/")
        full_upload_url = f"{base}/static{temp_video_path}"
        full_predicted_url = f"{base}/static{predicted_video_path}"

        # Add location data
        updated_result = location_service.add_random_locations(
            result, DEFAULT_LATITUDE, DEFAULT_LONGITUDE, MAX_DISTANCE_KM
        )

        print(f"Alert requested: {getAlert}")

        # Analyze alerts and get alert information
        alert_info = notification_service.process_alerts(updated_result)

        return JSONResponse(
            content={
                "video_data_uri": full_upload_url,
                "predicted_video_url": full_predicted_url,
                "results": updated_result,
                "alert_classes": alert_info["alert_classes"],
                "primary_alert_class": alert_info["primary_alert_class"],
                "is_poaching_detected": alert_info["is_poaching_detected"],
                # Keep backward compatibility
                "alert_class": alert_info["alert_class"],
            }
        )

    except Exception as e:
        return JSONResponse(
            status_code=500, content={"error": f"Error processing video: {str(e)}"}
        )


@api_router.get("/sample-data/")
async def get_sample_data():
    """
    Get sample detection data for testing.

    Returns:
        JSON response with sample data including alert information
    """
    sample_data = data_service.load_sample_data()
    updated_data = location_service.add_random_locations(sample_data)

    # Get alert information for sample data
    alert_info = notification_service.process_alerts(updated_data)

    return JSONResponse(
        content={
            "results": updated_data,
            "alert_classes": alert_info["alert_classes"],
            "primary_alert_class": alert_info["primary_alert_class"],
            "is_poaching_detected": alert_info["is_poaching_detected"],
            # Keep backward compatibility
            "alert_class": alert_info["alert_class"],
        }
    )
