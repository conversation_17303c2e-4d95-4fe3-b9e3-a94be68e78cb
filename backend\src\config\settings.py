"""
Configuration settings for Wildlife Anomaly Detection system.
"""

import os
from pathlib import Path

# Try to import torch, but handle gracefully if not available
try:
    import torch

    TORCH_AVAILABLE = True
except ImportError:
    torch = None
    TORCH_AVAILABLE = False

# Base paths
BASE_DIR = Path(__file__).parent.parent.parent
STATIC_DIR = BASE_DIR / "static"
WEIGHTS_DIR = BASE_DIR / "weights"
YOLOV5_DIR = BASE_DIR / "yolov5"

# Model paths
DAY_MODEL_PATH = WEIGHTS_DIR / "daylight.pt"
NIGHT_MODEL_PATH = WEIGHTS_DIR / "thermal.pt"

# Video processing
UPLOAD_DIR = STATIC_DIR / "upload"  # For uploaded videos
OUTPUT_DIR = STATIC_DIR / "out"  # For processed results

# Browser optimization settings
ENABLE_BROWSER_OPTIMIZATION = True  # Create H.264 optimized versions
REMOVE_ORIGINAL_AFTER_OPTIMIZATION = True  # Remove non-optimized version to save space

# Location settings
DEFAULT_LATITUDE = 22.606803
DEFAULT_LONGITUDE = 85.338173
MAX_DISTANCE_KM = 20

# Alert classes for wildlife monitoring
ALERT_CLASSES = ["Person", "Vehicle", "Fire"]


# Device settings - Auto-detect CUDA availability
def get_device():
    """
    Auto-detect the best available device.
    Priority: Environment Variable > CUDA GPU > CPU
    Returns device in format expected by YOLOv5: "0" for first GPU, "cpu" for CPU
    """
    # Check for environment variable override
    env_device = os.getenv("DEVICE", "").lower()
    if env_device in ["cpu", "0", "1", "2", "3"]:  # Support CPU and GPU device numbers
        print(f"🔧 Using device from environment: {env_device}")
        return env_device
    elif env_device == "cuda":
        print(f"🔧 Using device from environment: converting 'cuda' to '0'")
        return "0"  # Convert cuda to first GPU

    # Auto-detect if PyTorch is available
    if not TORCH_AVAILABLE:
        print(
            "⚠️ PyTorch not available, defaulting to CPU (will be validated at runtime)"
        )
        return "cpu"

    try:
        if torch.cuda.is_available():
            device = "0"  # Use first GPU (YOLOv5 expects device number, not "cuda")
            gpu_name = torch.cuda.get_device_name(0)
            print(f"🚀 Using GPU: {gpu_name} (device 0)")
        else:
            device = "cpu"
            print("💻 CUDA not available, using CPU")
        return device
    except Exception as e:
        print(f"⚠️ Error detecting device: {e}, falling back to CPU")
        return "cpu"


DEVICE = get_device()

# Class mappings
DAY_CLASS_TO_ANIMAL = {
    0: "Person",
    1: "Elephant",
    2: "Zebra",
    3: "Giraffe",
    4: "Deer",
    5: "Bison",
    6: "Rhino",
    7: "Boar",
    8: "Leopard",
    9: "Vehicle",
    10: "Fire",
}

THERMAL_CLASS_TO_ANIMAL = {
    0: "Person",
    1: "Elephant",
    2: "Deer",
    3: "Rhino",
    4: "Boar",
    5: "Leopard",
    6: "Vehicle",
    7: "Fire",
}

# Colors for bounding boxes
DETECTION_COLORS = [
    (255, 99, 71),
    (124, 252, 0),
    (255, 215, 0),
    (255, 255, 0),
    (0, 255, 255),
    (255, 0, 255),
    (255, 218, 185),
    (138, 43, 226),
    (255, 20, 147),
    (176, 196, 222),
    (0, 250, 154),
]
