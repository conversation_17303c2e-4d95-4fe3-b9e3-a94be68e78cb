"""
Model manager for loading and managing YOLO models.
"""

import torch
import pathlib
import functools
from typing import Tuple

from src.config.settings import DAY_MODEL_PATH, NIGHT_MODEL_PATH, DEVICE, YOLOV5_DIR


class ModelManager:
    """Manages YOLO model loading and initialization."""

    def __init__(self):
        self._day_model = None
        self._thermal_model = None
        self._device = self._validate_device()
        self._setup_torch_compatibility()

    def _validate_device(self):
        """Validate and potentially re-detect device at runtime."""
        device = DEVICE
        try:
            if device.isdigit():  # GPU device number (e.g., "0", "1")
                if torch.cuda.is_available() and int(device) < torch.cuda.device_count():
                    gpu_name = torch.cuda.get_device_name(int(device))
                    print(f"✅ Validated CUDA device {device}: {gpu_name}")
                    return device
                else:
                    print(f"⚠️ CUDA device {device} requested but not available, falling back to CPU")
                    return "cpu"
            elif device == "cpu":
                print(f"💻 Using CPU device")
                return "cpu"
            else:
                print(f"⚠️ Unknown device '{device}', using CPU")
                return "cpu"
        except Exception as e:
            print(f"⚠️ Error validating device: {e}, using CPU")
            return "cpu"

    def _setup_torch_compatibility(self):
        """Setup torch compatibility patches for YOLOv5."""
        # Handle Windows/POSIX path compatibility
        temp = pathlib.PosixPath
        pathlib.PosixPath = pathlib.WindowsPath

        # Monkey patch torch.load to handle PyTorch 2.6+ weights_only default change
        original_torch_load = torch.load

        @functools.wraps(torch.load)
        def patched_torch_load(*args, **kwargs):
            if "weights_only" not in kwargs:
                kwargs["weights_only"] = False
            return original_torch_load(*args, **kwargs)

        torch.load = patched_torch_load

    def load_models(self) -> Tuple[torch.nn.Module, torch.nn.Module]:
        """Load both day and thermal models."""
        if self._day_model is None:
            print("🔄 Loading day model for the first time...")
            self._day_model = self._load_day_model()
        else:
            print("✅ Day model already loaded, reusing existing instance")

        if self._thermal_model is None:
            print("🔄 Loading thermal model for the first time...")
            self._thermal_model = self._load_thermal_model()
        else:
            print("✅ Thermal model already loaded, reusing existing instance")

        return self._day_model, self._thermal_model

    def _load_day_model(self) -> torch.nn.Module:
        """Load the day model."""
        print(f"🔄 Loading day model on {self._device}...")
        return torch.hub.load(
            str(YOLOV5_DIR),
            "custom",
            path=str(DAY_MODEL_PATH),
            source="local",
            force_reload=True,
            trust_repo=True,
            device=self._device,
        )

    def _load_thermal_model(self) -> torch.nn.Module:
        """Load the thermal model."""
        print(f"🔄 Loading thermal model on {self._device}...")
        return torch.hub.load(
            str(YOLOV5_DIR),
            "custom",
            path=str(NIGHT_MODEL_PATH),
            source="local",
            force_reload=True,
            trust_repo=True,
            device=self._device,
        )

    def get_day_model(self) -> torch.nn.Module:
        """Get the day model."""
        if self._day_model is None:
            self.load_models()
        return self._day_model

    def get_thermal_model(self) -> torch.nn.Module:
        """Get the thermal model."""
        if self._thermal_model is None:
            self.load_models()
        return self._thermal_model

    @property
    def device(self) -> str:
        """Get the current device being used."""
        return self._device


# Global model manager instance
model_manager = ModelManager()
