# WildEye Backend

A FastAPI-based Wildlife Anomaly Detection API that processes video files for wildlife detection and poaching analysis using YOLOv5 models. The system supports both daylight and thermal imaging analysis with location-based alert generation.

## Features

- **Dual-Model Detection**: Supports both daylight and thermal imaging models
- **Wildlife Classification**: Detects animals (Elephant, Zebra, Giraffe, Deer, Bison, Rhino, Boar, <PERSON>pard) and threats (Person, Vehicle, Fire)
- **Poaching Alert System**: Automatically identifies potential poaching activities based on person/vehicle detection
- **Location Generation**: Adds random GPS coordinates within configurable radius for detected wildlife
- **Video Processing**: Processes uploaded videos and returns annotated results
- **GPU Acceleration**: CUDA support with automatic CPU fallback
- **Real-time Health Monitoring**: Model status and system health endpoints

## Architecture

```
backend/
├── src/
│   ├── api/routes.py           # API endpoint definitions
│   ├── config/settings.py      # Configuration and device detection
│   ├── core/model_manager.py   # YOLOv5 model loading and management
│   ├── models/schemas.py       # Data models and schemas
│   ├── services/               # Business logic services
│   │   ├── video_processor.py  # Video processing and inference
│   │   ├── location_service.py # GPS coordinate generation
│   │   ├── notification_service.py # Alert analysis
│   │   └── data_service.py     # Sample data handling
│   └── utils/file_handler.py   # File operations
├── yolov5/                     # YOLOv5 submodule
├── weights/                    # Pre-trained model weights
│   ├── daylight.pt            # Daylight detection model
│   └── thermal.pt             # Thermal imaging model
├── static/                     # Static files and uploads
├── data/                       # Sample video files
└── main.py                     # FastAPI application entry point
```

## Requirements

- **Python 3.12+** (project uses CPython 3.12 artifacts)
- **Git** (for submodule management)
- **uv** (recommended Python package installer - faster dependency resolution)
- **CUDA-compatible GPU** (optional, for accelerated inference)
- **FFmpeg** (for video processing optimization)

## Quick Setup

### 1. Clone with Submodules

```bash
git clone https://github.com/edgexhq/wildeye.git
cd wildeye/backend
git submodule update --init --recursive
```

### 2. Create Virtual Environment

**Windows (PowerShell):**

```powershell
uv venv
.\.venv\Scripts\Activate.ps1
```

**Linux/macOS:**

```bash
uv venv
source .venv/bin/activate
```

### 3. Install Dependencies

**Recommended: Using uv (faster and more reliable):**

```bash
# Install uv if not already installed
pip install uv

# Install main dependencies
uv pip install -r requirements.txt

# Install PyTorch with CUDA support (if available)

uv pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu129

# Install YOLOv5 dependencies (required for inference)
uv pip install -r yolov5/requirements.txt
```

**Alternative: Using pip:**

```bash
# Install main dependencies
pip install -r requirements.txt

# Install YOLOv5 dependencies (required for inference)
pip install -r yolov5/requirements.txt
```

### 4. Verify Model Weights

Ensure the pre-trained weights are present:

- `weights/daylight.pt` - Daylight detection model
- `weights/thermal.pt` - Thermal imaging model

## Running the Application

### Development Server

```bash
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### Production Server

```bash
python -m uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

### Access Points

- **API Documentation**: http://localhost:8000/docs (Scalar UI)
- **Health Check**: http://localhost:8000/health
- **Root Endpoint**: http://localhost:8000/

## Configuration

The application uses environment variables and configuration files in `src/config/settings.py`.

### Environment Variables

```bash
# Device Configuration (auto-detected if not set)
DEVICE=0                    # GPU device number (0, 1, 2...) or "cpu"

# Model Paths (defaults to weights/ directory)
MODEL_PATH_DAYLIGHT=weights/daylight.pt
MODEL_PATH_THERMAL=weights/thermal.pt

# Location Settings (for random coordinate generation)
DEFAULT_LATITUDE=22.606803
DEFAULT_LONGITUDE=85.338173
MAX_DISTANCE_KM=20

# Video Processing
ENABLE_BROWSER_OPTIMIZATION=true
REMOVE_ORIGINAL_AFTER_OPTIMIZATION=true
```

### Detection Classes

**Daylight Model Classes:**

- Animals: Elephant, Zebra, Giraffe, Deer, Bison, Rhino, Boar, Leopard
- Threats: Person, Vehicle, Fire

**Thermal Model Classes:**

- Animals: Elephant, Deer, Rhino, Boar, Leopard
- Threats: Person, Vehicle, Fire

### Alert System

The system automatically flags potential poaching activities when detecting:

- **Person** + **Vehicle** combinations
- **Person** near wildlife
- **Fire** detection

## Model Management

### Device Auto-Detection

```python
# Automatic device selection priority:
# 1. Environment variable (DEVICE)
# 2. First available CUDA GPU
# 3. CPU fallback
```

### Model Loading

- Models are preloaded on application startup for faster inference
- Supports both GPU and CPU inference
- Automatic fallback to CPU if GPU unavailable

### YOLOv5 Integration

The application uses YOLOv5 as a git submodule with custom patches for:

- Windows path compatibility
- PyTorch 2.7+ compatibility
- Custom class mappings for wildlife detection

## API Endpoints

### Core Endpoints

#### Root - GET `/`

Returns API information and available endpoints.

#### Health Check - GET `/health`

```json
{
  "status": "healthy",
  "service": "wildlife-detection-api",
  "models": {
    "day_model_loaded": true,
    "thermal_model_loaded": true,
    "device": "0"
  }
}
```

#### Video Upload - POST `/upload/`

Upload and process video for wildlife detection.

**Parameters:**

- `video` (file): Video file (MP4, AVI, MOV, WEBM, MKV)
- `selected_class` (form): "Daylight" or "Thermal"
- `getAlert` (form): Boolean for alert processing

**Example:**

```bash
curl -X POST "http://localhost:8000/upload/" \
  -F "video=@wildlife_video.mp4" \
  -F "selected_class=Daylight" \
  -F "getAlert=true"
```

**Response:**

```json
{
  "status": "success",
  "message": "Video processed successfully",
  "data": {
    "video_uuid": "uuid-string",
    "video_data_uri": "http://localhost:8000/static/upload/uuid.mp4",
    "predicted_video_url": "http://localhost:8000/static/out/uuid_predicted.mp4",
    "results": [
      {
        "frame_number": 1,
        "detections": [
          {
            "class_id": 1,
            "class_name": "Elephant",
            "confidence": 0.85,
            "latitude": 22.608123,
            "longitude": 85.340456
          }
        ]
      }
    ],
    "alert_classes": {
      "Person": [{ "confidence": 0.92 }]
    },
    "primary_alert_class": "Person",
    "is_poaching_detected": true
  }
}
```

## Development

### Project Structure

- **`main.py`**: FastAPI application with startup/shutdown events and static file serving
- **`src/api/routes.py`**: API endpoint definitions (currently integrated in main.py)
- **`src/config/settings.py`**: Configuration, device detection, and class mappings
- **`src/core/model_manager.py`**: YOLOv5 model loading with compatibility patches
- **`src/services/video_processor.py`**: Video processing and inference pipeline
- **`src/services/location_service.py`**: GPS coordinate generation within specified radius
- **`src/services/notification_service.py`**: Alert analysis and poaching detection
- **`src/models/schemas.py`**: Data models and type definitions

### Testing

Run the application and test with sample data:

```bash
# Start the server
python -m uvicorn main:app --reload

# Test health endpoint
curl http://localhost:8000/health

# Test sample data endpoint
curl http://localhost:8000/sample-data/

# Test file upload with sample video
curl -X POST "http://localhost:8000/upload/" \
  -F "video=@data/gettyimages-156035330-640_adpp.mp4" \
  -F "selected_class=Daylight" \
  -F "getAlert=true"
```

### YOLOv5 Submodule Management

```bash
# Update YOLOv5 to latest version
git submodule update --remote yolov5

# Initialize after cloning
git submodule update --init --recursive
```

## Deployment

### Docker

```dockerfile
FROM python:3.12-slim

WORKDIR /app
COPY . .

# Install system dependencies
RUN apt-get update && apt-get install -y git ffmpeg && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
RUN pip install -r requirements.txt && pip install -r yolov5/requirements.txt

# Create directories
RUN mkdir -p static/upload static/out

EXPOSE 8000
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Production Considerations

- Mount model weights as external volumes
- Configure reverse proxy for static file serving
- Enable GPU support for CUDA inference
- Use process managers for scaling
- Set up monitoring and log rotation

## Troubleshooting

- **Import Errors**: Ensure YOLOv5 submodule is initialized: `git submodule update --init --recursive`
- **CUDA Issues**: Verify PyTorch CUDA compatibility with your drivers
- **Model Loading**: Check that weights files exist in `weights/` directory
- **Memory Issues**: Reduce batch size or use CPU inference for limited resources

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make changes and test thoroughly
4. Submit a pull request with detailed description

## License

See the main repository LICENSE file. YOLOv5 submodule has its own license terms.
