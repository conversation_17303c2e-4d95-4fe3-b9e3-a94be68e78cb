# WildEye Backend Deployment Guide - Vultr Cloud

This guide provides comprehensive instructions for deploying the WildEye Wildlife Detection API on Vultr cloud infrastructure, optimized for GPU-accelerated inference and production workloads.

## Vultr Server Configuration

### Recommended Server Specifications

#### GPU Instance (Recommended for Production)

```
Instance Type: Vultr Cloud GPU
GPU Options:
  - NVIDIA H100 (80GB HBM3) - Latest Hopper architecture
  - NVIDIA A100 (40GB/80GB) - Ampere architecture
  - NVIDIA RTX A6000 (48GB) - Ada Lovelace architecture
  - NVIDIA RTX 4090 (24GB) - Ada Lovelace architecture
  - NVIDIA L40S (48GB) - Ada Lovelace architecture
vCPU: 8+ cores
RAM: 32GB+
Storage: 200GB NVMe SSD
OS: Ubuntu 22.04 LTS or Ubuntu 24.04 LTS
Region: Choose closest to your users (New York, London, Tokyo, etc.)
```

**Estimated Cost**: $1.50-4.50/hour (~$1,100-3,300/month) depending on GPU tier

#### CPU-Only Instance (Development/Testing)

```
Instance Type: Vultr High Frequency Compute
vCPU: 8 vCPUs
RAM: 16GB
Storage: 160GB NVMe SSD
OS: Ubuntu 22.04 LTS
Bandwidth: 4TB
```

**Estimated Cost**: $96/month

### Network Configuration

- **Firewall Rules**:
  - SSH: Port 22 (Your IP only)
  - HTTP: Port 80 (0.0.0.0/0)
  - HTTPS: Port 443 (0.0.0.0/0)
  - API: Port 8000 (0.0.0.0/0) - Optional for direct access

## Initial Server Setup

### 1. Connect to Server

```bash
ssh root@YOUR_VULTR_IP
```

### 2. Update System & Install Dependencies

```bash
# Update system
apt update && apt upgrade -y

# Install essential packages
apt install -y \
    git \
    curl \
    wget \
    htop \
    nginx \
    certbot \
    python3-certbot-nginx \
    ufw \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release

# Install Python 3.12 (if not available)
add-apt-repository ppa:deadsnakes/ppa -y
apt update
apt install -y python3.12 python3.12-venv python3.12-dev python3-pip

# Install FFmpeg for video processing
apt install -y ffmpeg

# Install Docker (optional, for containerized deployment)
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
usermod -aG docker root
```

### 3. GPU Setup (GPU Instance Only)

```bash
# Install NVIDIA drivers (latest stable)
apt install -y ubuntu-drivers-common
ubuntu-drivers devices
ubuntu-drivers autoinstall

# Install CUDA Toolkit 12.4 (latest stable as of 2025)
wget https://developer.download.nvidia.com/compute/cuda/12.4.0/local_installers/cuda_12.4.0_550.54.14_linux.run
chmod +x cuda_12.4.0_550.54.14_linux.run
sh cuda_12.4.0_550.54.14_linux.run --silent --toolkit

# Add CUDA to PATH
echo 'export PATH=/usr/local/cuda-12.4/bin:$PATH' >> ~/.bashrc
echo 'export LD_LIBRARY_PATH=/usr/local/cuda-12.4/lib64:$LD_LIBRARY_PATH' >> ~/.bashrc
echo 'export CUDA_HOME=/usr/local/cuda-12.4' >> ~/.bashrc
source ~/.bashrc

# Verify CUDA installation
nvidia-smi
nvcc --version

# Test CUDA compute capability
python3 -c "
import subprocess
result = subprocess.run(['nvidia-smi', '--query-gpu=compute_cap', '--format=csv,noheader'], capture_output=True, text=True)
print(f'CUDA Compute Capability: {result.stdout.strip()}')
"
```

### 4. Setup Application User

```bash
# Create application user
useradd -m -s /bin/bash wildeye
usermod -aG docker wildeye  # If using Docker
passwd wildeye  # Set password

# Switch to application user
su - wildeye
```

## Application Deployment

### 1. Clone Repository

```bash
# As wildeye user
cd /home/<USER>
git clone https://github.com/edgexhq/wildeye.git
cd wildeye/backend
git submodule update --init --recursive
```

### 2. Setup Python Environment

```bash
# Create virtual environment
python3.12 -m venv .venv
source .venv/bin/activate

# Install uv for faster package management
pip install uv

# Install dependencies with CUDA 12.4 support
uv pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu124
uv pip install -r requirements.txt
uv pip install -r yolov5/requirements.txt

# Verify PyTorch CUDA support (GPU instance only)
python -c "
import torch
print(f'PyTorch version: {torch.__version__}')
print(f'CUDA available: {torch.cuda.is_available()}')
print(f'CUDA version: {torch.version.cuda}')
print(f'GPU count: {torch.cuda.device_count()}')
if torch.cuda.is_available():
    for i in range(torch.cuda.device_count()):
        print(f'GPU {i}: {torch.cuda.get_device_name(i)}')
        print(f'  Memory: {torch.cuda.get_device_properties(i).total_memory / 1024**3:.1f} GB')
        print(f'  Compute capability: {torch.cuda.get_device_properties(i).major}.{torch.cuda.get_device_properties(i).minor}')
"
```

### 3. Setup Model Weights

```bash
# Create weights directory if not exists
mkdir -p weights

# Upload your model weights to the server
# Method 1: SCP from local machine
# scp daylight.pt thermal.pt wildeye@YOUR_VULTR_IP:/home/<USER>/wildeye/backend/weights/

# Method 2: Download from cloud storage (example)
# cd weights
# wget https://your-storage-url.com/daylight.pt
# wget https://your-storage-url.com/thermal.pt
```

### 4. Environment Configuration

```bash
# Create environment file
cat > .env << 'EOF'
# Device Configuration
DEVICE=0  # Use first GPU, set to "cpu" for CPU-only instance

# Model Paths
MODEL_PATH_DAYLIGHT=weights/daylight.pt
MODEL_PATH_THERMAL=weights/thermal.pt

# Location Settings
DEFAULT_LATITUDE=22.606803
DEFAULT_LONGITUDE=85.338173
MAX_DISTANCE_KM=20

# Video Processing
ENABLE_BROWSER_OPTIMIZATION=true
REMOVE_ORIGINAL_AFTER_OPTIMIZATION=true

# API Settings
API_HOST=0.0.0.0
API_PORT=8000

# CUDA Settings (for latest GPU support)
CUDA_VISIBLE_DEVICES=0
PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128

# Logging
LOG_LEVEL=INFO
EOF
```

### 5. Create Necessary Directories

```bash
mkdir -p static/upload static/out logs
chmod 755 static static/upload static/out
```

## Production Setup

### 1. Create Systemd Service

```bash
# As root user
sudo tee /etc/systemd/system/wildeye.service << 'EOF'
[Unit]
Description=WildEye Wildlife Detection API
After=network.target

[Service]
Type=exec
User=wildeye
Group=wildeye
WorkingDirectory=/home/<USER>/wildeye/backend
Environment=PATH=/home/<USER>/wildeye/backend/.venv/bin
EnvironmentFile=/home/<USER>/wildeye/backend/.env
ExecStart=/home/<USER>/wildeye/backend/.venv/bin/python -m uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/home/<USER>/wildeye/backend/static /home/<USER>/wildeye/backend/logs

[Install]
WantedBy=multi-user.target
EOF

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable wildeye
sudo systemctl start wildeye

# Check status
sudo systemctl status wildeye
```

### 2. Setup Nginx Reverse Proxy

#### Option A: IP Address Only (No Domain)

```bash
# Create nginx configuration for IP-only access
sudo tee /etc/nginx/sites-available/wildeye << 'EOF'
upstream wildeye_backend {
    server 127.0.0.1:8000;
}

server {
    listen 80 default_server;
    listen [::]:80 default_server;

    # Accept any server name (IP address)
    server_name _;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;

    # Client max body size (for video uploads)
    client_max_body_size 500M;

    # API proxy
    location / {
        proxy_pass http://wildeye_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # Timeouts for video processing
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }

    # Static files
    location /static/ {
        alias /home/<USER>/wildeye/backend/static/;
        expires 1d;
        add_header Cache-Control "public, immutable";

        # Video file specific settings
        location ~* \.(mp4|avi|mov|webm|mkv)$ {
            add_header Accept-Ranges bytes;
            expires 1h;
        }
    }

    # Health check endpoint
    location /health {
        proxy_pass http://wildeye_backend;
        access_log off;
    }
}
EOF

# Enable site
sudo ln -s /etc/nginx/sites-available/wildeye /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# Test nginx configuration
sudo nginx -t
sudo systemctl restart nginx

echo "🎉 WildEye API is now accessible at: http://YOUR_VULTR_IP"
echo "📋 API Documentation: http://YOUR_VULTR_IP/docs"
echo "❤️ Health Check: http://YOUR_VULTR_IP/health"
```

#### Option B: With Custom Domain (If You Have One)

```bash
# Create nginx configuration with domain
sudo tee /etc/nginx/sites-available/wildeye << 'EOF'
upstream wildeye_backend {
    server 127.0.0.1:8000;
}

server {
    listen 80;
    server_name YOUR_DOMAIN.com www.YOUR_DOMAIN.com;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;

    # Client max body size (for video uploads)
    client_max_body_size 500M;

    # API proxy
    location / {
        proxy_pass http://wildeye_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # Timeouts for video processing
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }

    # Static files
    location /static/ {
        alias /home/<USER>/wildeye/backend/static/;
        expires 1d;
        add_header Cache-Control "public, immutable";

        # Video file specific settings
        location ~* \.(mp4|avi|mov|webm|mkv)$ {
            add_header Accept-Ranges bytes;
            expires 1h;
        }
    }

    # Health check endpoint
    location /health {
        proxy_pass http://wildeye_backend;
        access_log off;
    }
}
EOF

# Enable site
sudo ln -s /etc/nginx/sites-available/wildeye /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# Test nginx configuration
sudo nginx -t
sudo systemctl restart nginx
```

### 3. SSL Certificate Setup

#### Option A: Self-Signed Certificate (IP Address Access)

```bash
# Create self-signed certificate for HTTPS (optional for IP-only access)
sudo mkdir -p /etc/ssl/private /etc/ssl/certs

# Generate self-signed certificate
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/ssl/private/wildeye-selfsigned.key \
    -out /etc/ssl/certs/wildeye-selfsigned.crt \
    -subj "/C=US/ST=State/L=City/O=Organization/OU=OrgUnit/CN=YOUR_VULTR_IP"

# Create strong Diffie-Hellman group
sudo openssl dhparam -out /etc/ssl/certs/dhparam.pem 2048

# Update nginx to use HTTPS
sudo tee -a /etc/nginx/sites-available/wildeye << 'EOF'

# HTTPS server block
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name _;

    # SSL Configuration
    ssl_certificate /etc/ssl/certs/wildeye-selfsigned.crt;
    ssl_certificate_key /etc/ssl/private/wildeye-selfsigned.key;
    ssl_dhparam /etc/ssl/certs/dhparam.pem;

    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Copy all location blocks from HTTP configuration above
    # ... (same as HTTP server block)
}
EOF

sudo nginx -t
sudo systemctl restart nginx

echo "🔒 HTTPS available at: https://YOUR_VULTR_IP (self-signed certificate)"
echo "⚠️  Browsers will show security warning - click 'Advanced' → 'Continue'"
```

#### Option B: Let's Encrypt (Only with Domain)

```bash
# Only run this if you have a domain pointing to your server
# Install SSL certificate with Let's Encrypt
sudo certbot --nginx -d YOUR_DOMAIN.com -d www.YOUR_DOMAIN.com

# Verify automatic renewal
sudo certbot renew --dry-run

echo "🔒 HTTPS available at: https://YOUR_DOMAIN.com"
```

#### Option C: No HTTPS (HTTP Only)

```bash
# If you prefer HTTP only (not recommended for production)
echo "📡 API accessible via HTTP only"
echo "🌐 Access your API at: http://YOUR_VULTR_IP"
echo "⚠️  Consider adding HTTPS for production use"
```

### 4. Firewall Configuration

```bash
# Setup UFW firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp   # HTTP
sudo ufw allow 443/tcp  # HTTPS (if using SSL)
sudo ufw enable

# Check status
sudo ufw status verbose
```

## Testing Your Deployment

### 1. Basic Health Check

```bash
# Get your server IP
VULTR_IP=$(curl -s ifconfig.me)
echo "Your server IP: $VULTR_IP"

# Test API health
curl -s http://$VULTR_IP/health | jq '.'

# Test API documentation
curl -s http://$VULTR_IP/ | jq '.'
```

### 2. Test Video Upload

```bash
# Upload a test video (replace with actual video file)
curl -X POST "http://$VULTR_IP/upload/" \
  -F "video=@/path/to/test-video.mp4" \
  -F "selected_class=Daylight" \
  -F "getAlert=true"
```

### 3. Access Web Interface

Open your browser and navigate to:

- **API Documentation**: `http://YOUR_VULTR_IP/docs`
- **Health Check**: `http://YOUR_VULTR_IP/health`
- **Root API**: `http://YOUR_VULTR_IP/`

## Getting a Domain Name Later (Optional)

### Free Domain Options

1. **Freenom** (free .tk, .ml, .ga domains)
2. **Subdomain services**:
   - `your-app.ngrok.io` (temporary)
   - `your-app.herokuapp.com` style services

### Paid Domain Options

1. **Vultr DNS** - Manage domains directly in Vultr
2. **Namecheap** - $8-12/year for .com domains
3. **Cloudflare** - Domain registration + CDN
4. **Google Domains** - Simple domain management

### Setting Up Domain After Deployment

```bash
# 1. Point your domain's A record to your Vultr IP
# Domain: YOUR_DOMAIN.com → A Record → YOUR_VULTR_IP

# 2. Update nginx configuration
sudo sed -i 's/server_name _;/server_name YOUR_DOMAIN.com www.YOUR_DOMAIN.com;/' /etc/nginx/sites-available/wildeye

# 3. Add SSL certificate
sudo certbot --nginx -d YOUR_DOMAIN.com -d www.YOUR_DOMAIN.com

# 4. Test the domain
curl https://YOUR_DOMAIN.com/health
```

## Docker Deployment (Alternative)

### 1. Create Dockerfile

```dockerfile
FROM nvidia/cuda:12.4-runtime-ubuntu22.04

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3.12 \
    python3.12-venv \
    python3-pip \
    git \
    ffmpeg \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy application
COPY . .

# Install Python dependencies with CUDA 12.4 support
RUN python3.12 -m pip install uv
RUN uv pip install --system torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu124
RUN uv pip install --system -r requirements.txt
RUN uv pip install --system -r yolov5/requirements.txt

# Create necessary directories
RUN mkdir -p static/upload static/out logs
RUN chmod 755 static static/upload static/out

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/health || exit 1

# Run application
CMD ["python3.12", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 2. Docker Compose Setup

```yaml
# docker-compose.yml
version: "3.8"

services:
  wildeye:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEVICE=0
      - MODEL_PATH_DAYLIGHT=weights/daylight.pt
      - MODEL_PATH_THERMAL=weights/thermal.pt
    volumes:
      - ./weights:/app/weights
      - ./static:/app/static
      - ./logs:/app/logs
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

### 3. Deploy with Docker

```bash
# Build and run
docker-compose up -d

# Check logs
docker-compose logs -f wildeye

# Scale for load balancing
docker-compose up -d --scale wildeye=3
```

## Monitoring & Maintenance

### 1. Log Management

```bash
# Setup log rotation
sudo tee /etc/logrotate.d/wildeye << 'EOF'
/home/<USER>/wildeye/backend/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 wildeye wildeye
    postrotate
        sudo systemctl reload wildeye
    endscript
}
EOF
```

### 2. Monitoring Setup

```bash
# Install monitoring tools
sudo apt install -y htop nvtop iotop

# Create monitoring script
tee /home/<USER>/monitor.sh << 'EOF'
#!/bin/bash
echo "=== System Status ==="
date
echo "CPU Usage:" && top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}'
echo "Memory Usage:" && free -m | awk 'NR==2{printf "%.2f%%\n", $3*100/$2}'
echo "Disk Usage:" && df -h | awk '$NF=="/"{printf "%s\n", $5}'

echo -e "\n=== GPU Status ==="
nvidia-smi --query-gpu=utilization.gpu,memory.used,memory.total,temperature.gpu --format=csv,noheader,nounits

echo -e "\n=== Service Status ==="
systemctl is-active wildeye
systemctl is-active nginx

echo -e "\n=== Recent Errors ==="
journalctl -u wildeye --since "1 hour ago" --no-pager | grep -i error | tail -5
EOF

chmod +x /home/<USER>/monitor.sh
```

### 3. Backup Strategy

```bash
# Create backup script
tee /home/<USER>/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/backup/wildeye"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup application code
tar -czf $BACKUP_DIR/wildeye_app_$DATE.tar.gz \
    --exclude='.venv' \
    --exclude='static/upload/*' \
    --exclude='static/out/*' \
    /home/<USER>/wildeye/

# Backup weights
tar -czf $BACKUP_DIR/wildeye_weights_$DATE.tar.gz \
    /home/<USER>/wildeye/backend/weights/

# Backup configuration
cp /etc/nginx/sites-available/wildeye $BACKUP_DIR/nginx_config_$DATE
cp /etc/systemd/system/wildeye.service $BACKUP_DIR/systemd_config_$DATE
cp /home/<USER>/wildeye/backend/.env $BACKUP_DIR/env_config_$DATE

# Clean old backups (keep last 7 days)
find $BACKUP_DIR -name "wildeye_*" -mtime +7 -delete

echo "Backup completed: $DATE"
EOF

chmod +x /home/<USER>/backup.sh

# Setup daily backup cron job
crontab -e
# Add: 0 2 * * * /home/<USER>/backup.sh >> /home/<USER>/backup.log 2>&1
```

## Performance Optimization

### 1. System Tuning

```bash
# Optimize kernel parameters
sudo tee -a /etc/sysctl.conf << 'EOF'
# Network optimizations
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.ipv4.tcp_rmem = 4096 87380 134217728
net.ipv4.tcp_wmem = 4096 65536 134217728
net.ipv4.tcp_congestion_control = bbr

# File descriptor limits
fs.file-max = 65536
EOF

sudo sysctl -p
```

### 2. GPU Performance Optimization

```bash
# Set GPU performance mode for modern GPUs
sudo nvidia-smi -pm 1  # Enable persistence mode
sudo nvidia-smi -ac 1215,1410  # Set memory and graphics clocks (adjust for your GPU)

# For H100/A100 GPUs, enable Multi-Instance GPU (MIG) if needed
sudo nvidia-smi -mig 1  # Enable MIG mode (optional)

# Optimize CUDA memory allocation
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
export CUDA_LAUNCH_BLOCKING=0  # For production (set to 1 for debugging)

# Enable GPU memory growth for TensorFlow models (if using TF alongside PyTorch)
export TF_FORCE_GPU_ALLOW_GROWTH=true
```

### 3. PyTorch Optimization for Latest Hardware

```bash
# Create GPU optimization script
tee /home/<USER>/optimize_gpu.py << 'EOF'
import torch
import os

def optimize_for_gpu():
    if not torch.cuda.is_available():
        print("No GPU available")
        return

    # Enable optimizations for modern GPUs
    torch.backends.cudnn.benchmark = True  # Optimize for consistent input sizes
    torch.backends.cuda.matmul.allow_tf32 = True  # Enable TF32 on A100/H100
    torch.backends.cudnn.allow_tf32 = True

    # For H100/A100 with FP8 support
    if hasattr(torch.backends.cuda, 'enable_fp8'):
        torch.backends.cuda.enable_fp8 = True

    # Memory management
    torch.cuda.empty_cache()

    device = torch.cuda.current_device()
    props = torch.cuda.get_device_properties(device)

    print(f"GPU: {props.name}")
    print(f"Compute Capability: {props.major}.{props.minor}")
    print(f"Memory: {props.total_memory / 1024**3:.1f} GB")
    print(f"TF32 enabled: {torch.backends.cuda.matmul.allow_tf32}")
    print(f"cuDNN benchmark: {torch.backends.cudnn.benchmark}")

    # Recommend optimal settings based on GPU
    if props.major >= 8:  # Ampere, Ada, Hopper architectures
        print("✅ Modern GPU detected - all optimizations enabled")
    else:
        print("⚠️ Older GPU - some optimizations may not be available")

if __name__ == "__main__":
    optimize_for_gpu()
EOF

python3 /home/<USER>/optimize_gpu.py
```

### 4. Application Scaling

```bash
# For high-traffic scenarios, use Gunicorn with Uvicorn workers
pip install gunicorn

# Update systemd service to use Gunicorn with GPU-optimized settings
sudo tee /etc/systemd/system/wildeye.service << 'EOF'
[Unit]
Description=WildEye Wildlife Detection API
After=network.target

[Service]
Type=exec
User=wildeye
Group=wildeye
WorkingDirectory=/home/<USER>/wildeye/backend
Environment=PATH=/home/<USER>/wildeye/backend/.venv/bin
Environment=CUDA_VISIBLE_DEVICES=0
Environment=PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
EnvironmentFile=/home/<USER>/wildeye/backend/.env
ExecStart=/home/<USER>/wildeye/backend/.venv/bin/gunicorn main:app -w 2 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000 --timeout 300 --worker-connections 1000
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10

# Resource limits optimized for GPU workloads
LimitNOFILE=65536
LimitNPROC=4096
MemoryMax=8G

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/home/<USER>/wildeye/backend/static /home/<USER>/wildeye/backend/logs

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl daemon-reload
sudo systemctl restart wildeye
```

## Troubleshooting

### Common Issues

1. **GPU Not Detected**

   ```bash
   # Check CUDA installation and driver compatibility
   nvidia-smi
   nvidia-ml-py3 -c "import pynvml; pynvml.nvmlInit(); print('GPU driver OK')"
   python -c "import torch; print(f'PyTorch CUDA: {torch.cuda.is_available()}'); print(f'CUDA version: {torch.version.cuda}')"

   # For latest GPUs, ensure driver version compatibility
   # H100 requires driver >= 525.60.13
   # RTX 4090 requires driver >= 520.61.05
   # Check driver version
   cat /proc/driver/nvidia/version

   # Reinstall CUDA if needed
   sudo apt purge nvidia-cuda-toolkit nvidia-*
   # Follow GPU setup steps again with latest CUDA
   ```

2. **CUDA Out of Memory (Modern GPU Optimization)**

   ```bash
   # Monitor GPU memory usage
   nvidia-smi -l 1
   watch -n1 'nvidia-smi --query-gpu=memory.used,memory.total,utilization.gpu --format=csv'

   # Enable memory-efficient settings for large models
   export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:64,expandable_segments:True

   # For H100/A100 with large memory, enable unified memory
   export CUDA_MANAGED_FORCE_DEVICE_ALLOC=1
   ```

3. **Performance Issues with Latest GPUs**

   ```bash
   # Check if GPU is running at full performance
   nvidia-smi -q -d PERFORMANCE

   # Enable maximum performance mode
   sudo nvidia-smi -pm 1
   sudo nvidia-smi --auto-boost-default=DISABLED
   sudo nvidia-smi -ac 1215,1410  # Adjust based on your GPU specs

   # For H100, enable MIG mode for multiple workloads
   sudo nvidia-smi -mig 1
   sudo nvidia-smi mig -cgi 19,19,19,19,19,19,19  # Create 7 MIG instances
   ```

4. **Mixed Precision Issues (FP16/BF16/FP8)**

   ```bash
   # Test mixed precision capabilities
   python -c "
   import torch
   if torch.cuda.is_available():
       device = torch.device('cuda')
       # Test FP16
       try:
           x = torch.randn(1000, 1000, device=device, dtype=torch.float16)
           y = torch.mm(x, x)
           print('FP16 supported')
       except:
           print('FP16 issues')

       # Test BF16 (available on A100, H100)
       try:
           x = torch.randn(1000, 1000, device=device, dtype=torch.bfloat16)
           y = torch.mm(x, x)
           print('BF16 supported')
       except:
           print('BF16 not supported')
   "
   ```

5. **High Memory Usage**

   ```bash
   # Monitor memory usage
   free -h
   ps aux --sort=-%mem | head -10

   # Reduce worker count in systemd service
   sudo systemctl edit wildeye
   ```

6. **Slow Video Processing**

   ```bash
   # Check GPU utilization
   nvidia-smi -l 1

   # Verify FFmpeg GPU acceleration
   ffmpeg -hwaccels
   ```

7. **Upload Timeouts**
   ```bash
   # Increase nginx timeouts
   sudo vim /etc/nginx/sites-available/wildeye
   # Add: proxy_read_timeout 600s;
   ```

### Performance Monitoring

```bash
# Real-time GPU monitoring
watch -n1 nvidia-smi

# API performance testing
curl -w "@curl-format.txt" -s -o /dev/null http://YOUR_DOMAIN/health

# Log analysis
tail -f /var/log/nginx/access.log | grep -E "(upload|/static/)"
```

## Security Considerations

1. **Regular Updates**

   ```bash
   sudo apt update && sudo apt upgrade -y
   pip list --outdated
   ```

2. **Access Control**

   - Use Vultr's firewall in addition to UFW
   - Implement API rate limiting
   - Consider VPN access for admin operations

3. **Data Protection**
   - Enable automatic backups to Vultr Object Storage
   - Implement data retention policies
   - Use encrypted storage for sensitive model weights

## Cost Optimization

1. **Instance Right-sizing**

   - Monitor resource usage and adjust instance size
   - Use CPU instances for development/testing
   - Consider spot instances for non-critical workloads

2. **Storage Optimization**

   - Regular cleanup of processed videos
   - Use object storage for long-term backup
   - Implement automatic file cleanup policies

3. **Network Optimization**
   - Use Vultr's CDN for static content
   - Enable gzip compression
   - Optimize video compression settings

---

## Quick Start Commands

### For IP-Only Deployment (No Domain Required)

```bash
# Connect to your Vultr server
ssh root@YOUR_VULTR_IP

# Run the complete setup (all-in-one script)
curl -sSL https://raw.githubusercontent.com/yourusername/wildeye/main/scripts/vultr-setup-ip.sh | bash

# Alternative: Manual setup
apt update && apt upgrade -y
# Follow the step-by-step guide above

# Check deployment status
systemctl status wildeye nginx
journalctl -fu wildeye

# Get your API endpoints
echo "🎉 WildEye API Endpoints:"
echo "📋 Documentation: http://$(curl -s ifconfig.me)/docs"
echo "❤️  Health Check: http://$(curl -s ifconfig.me)/health"
echo "🔗 Root API: http://$(curl -s ifconfig.me)/"
```

### For Domain-Based Deployment

```bash
# Same as above, but use Option B configurations
# Remember to point your domain's A record to your Vultr IP first
```

## IP-Based Deployment Patterns

### Development & Testing

```bash
# Simple HTTP access for development
API_URL="http://YOUR_VULTR_IP"
curl $API_URL/health
```

### Client Applications

```javascript
// Frontend configuration
const API_BASE_URL = "http://YOUR_VULTR_IP";

// Example fetch
fetch(`${API_BASE_URL}/upload/`, {
  method: "POST",
  body: formData,
});
```

### Mobile App Integration

```python
# Python client example
import requests

API_BASE = "http://YOUR_VULTR_IP"

def upload_video(video_path, video_class="Daylight"):
    with open(video_path, 'rb') as f:
        files = {'video': f}
        data = {
            'selected_class': video_class,
            'getAlert': True
        }
        response = requests.post(f"{API_BASE}/upload/", files=files, data=data)
    return response.json()
```

### Load Balancer Setup (Multiple Servers)

```bash
# If you scale to multiple Vultr instances
# Use Vultr Load Balancer or nginx upstream
upstream wildeye_cluster {
    server YOUR_VULTR_IP_1:8000;
    server YOUR_VULTR_IP_2:8000;
    server YOUR_VULTR_IP_3:8000;
}
```

## Cost Optimization for IP-Only Deployment

### Advantages of IP-Only Setup

- **No domain costs** ($10-15/year saved)
- **No DNS management** complexity
- **Immediate deployment** - no waiting for DNS propagation
- **Perfect for APIs** and backend services
- **Easy testing** and development

### When to Consider a Domain

- **Public-facing application**
- **Professional branding** requirements
- **SSL certificate needs** (though self-signed works for APIs)
- **Multiple environments** (dev.domain.com, api.domain.com)
- **CDN integration** requirements
