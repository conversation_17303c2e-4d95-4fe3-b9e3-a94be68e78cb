/* eslint-disable @next/next/no-img-element */
"use client";

import type React from "react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  Upload,
  Video,
  Shield,
  Zap,
  Eye,
  MapPin,
  AlertTriangle,
  Car,
  Camera,
  Target,
  Loader,
} from "lucide-react";
import { toast } from "sonner";

// Define the structure of the API response
// Single object detection in a frame
export interface Detection {
  class_id: number;
  class_name: string;
  confidence: number;
  latitude: number;
  longitude: number;
}

// Results per video frame
export interface FrameResult {
  frame_number: number;
  detections: Detection[];
}

// Aggregated statistics for a detected class
export interface AlertClassStats {
  class_name: string;
  mean_confidence: number;
  detection_count: number;
  confidence_scores: number[];
}

// Collection of alert class stats, keyed by class name
export interface AlertClasses {
  [className: string]: AlertClassStats[];
}

// Full API response structure
export interface ApiResponse {
  status?: string; // Optional, e.g., "success"
  message?: string; // Optional, e.g., "Video processed successfully"
  video_uuid: string;
  video_data_uri: string;
  predicted_video_url: string;
  results?: FrameResult[];
  alert_classes?: AlertClasses; // Aggregated alert stats
  primary_alert_class: string;
  is_poaching_detected: boolean;
  alert_class: string; // The main alert class
}

export default function WildlifeDetection() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [timeOfDay, setTimeOfDay] = useState("daylight"); // Default to daylight
  const [isLoading, setIsLoading] = useState(false);
  const [apiResponse, setApiResponse] = useState<ApiResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 100 * 1024 * 1024) {
        // 100 MB size limit
        toast.error("File size should not exceed 100MB.");
        return;
      }
      setSelectedFile(file);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      toast.error("Please select a video file first.");
      return;
    }
    if (!timeOfDay) {
      toast.error("Please select the time of day.");
      return;
    }

    setIsLoading(true);
    setError(null);
    setApiResponse(null);

    const formData = new FormData();
    formData.append("video", selectedFile);
    formData.append("selected_class", timeOfDay);
    formData.append("getAlert", "true"); // Always true as per requirements

    try {
      const response = await fetch("http://localhost:8000/upload/", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to process video.");
      }

      const responseData = await response.json();
      // Handle nested data structure
      const data: ApiResponse = responseData.data || responseData;
      setApiResponse(data);
      toast.success("Video processed successfully!");
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred.";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const features = [
    {
      icon: Eye,
      title: "Stealth Surveillance",
      description:
        "Efficient monitoring system that operates covertly to safeguard wildlife habitats.",
    },
    {
      icon: Shield,
      title: "Firefighter AI",
      description:
        "Cutting-edge technology that detects and responds to wildfires in real-time, minimizing environmental damage.",
    },
    {
      icon: Target,
      title: "Poacher Buster",
      description:
        "Dedicated AI solution designed to capture illegal poaching activities and protect endangered species.",
    },
    {
      icon: Camera,
      title: "Night Guardian",
      description:
        "Vigilant nighttime surveillance system using thermal imaging to safeguard wildlife during the dark hours.",
    },
    {
      icon: MapPin,
      title: "Wildlife Watcher",
      description:
        "Intelligent observer that monitors and studies the behavior of diverse wildlife species.",
    },
    {
      icon: Zap,
      title: "Smart Sentry",
      description:
        "Intelligent sentinels that detect and report intrusions or threats to wildlife habitats.",
    },
    {
      icon: AlertTriangle,
      title: "Anomaly Alert",
      description:
        "Rapid detection and alert system for suspicious activities, enhancing wildlife protection.",
    },
    {
      icon: Car,
      title: "Vehicle Vigilant",
      description:
        "Advanced system that monitors unauthorized vehicles in protected wildlife areas.",
    },
  ];

  const getDetectionRows = () => {
    if (!apiResponse || !apiResponse.results) return [];
    const rows: {
      index: number;
      frame: string;
      object: string;
      confidence: string;
      latitude: string;
      longitude: string;
    }[] = [];
    let index = 1;
    apiResponse.results.forEach((result) => {
      result.detections.forEach((detection) => {
        rows.push({
          index: index++,
          frame: `Frame ${result.frame_number}`,
          object: detection.class_name,
          confidence: `${(detection.confidence * 100).toFixed(1)}%`,
          latitude: detection.latitude?.toFixed(6) ?? "N/A",
          longitude: detection.longitude?.toFixed(6) ?? "N/A",
        });
      });
    });
    return rows;
  };

  const resultData = getDetectionRows();

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-4xl font-bold">
            <span className="text-green-600">Wildlife</span> Anomaly Detection
            Using YOLO
          </h1>
        </div>

        {/* Upload Section */}
        <Card className="w-full max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Upload Video
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="video-upload">
                Upload Video (.mp4, max 30s, max 100MB)
              </Label>
              <div className="flex items-center gap-4">
                <Input
                  id="video-upload"
                  type="file"
                  accept="video/mp4"
                  onChange={handleFileChange}
                  className="flex-1"
                  disabled={isLoading}
                />
                <Badge variant="outline" className="flex items-center gap-1">
                  <Video className="h-3 w-3" />
                  Alert & Notification
                </Badge>
              </div>
              {selectedFile && (
                <p className="text-sm text-muted-foreground">
                  Selected: {selectedFile.name}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="time-select">Time of Day</Label>
              <Select
                value={timeOfDay}
                onValueChange={setTimeOfDay}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Day/Night" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daylight">Day</SelectItem>
                  <SelectItem value="thermal">Night</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button
              className="w-full bg-green-600 hover:bg-green-700"
              size="lg"
              onClick={handleUpload}
              disabled={isLoading}
            >
              {isLoading ? (
                <span className="flex items-center">
                  <Loader className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </span>
              ) : (
                "Upload and Process"
              )}
            </Button>
            {error && <p className="text-sm text-red-500 mt-2">{error}</p>}
          </CardContent>
        </Card>

        <pre>{JSON.stringify(apiResponse, null, 2)}</pre>

        {/* Visualization Section */}
        {apiResponse && (
          <Card>
            <CardHeader className="bg-green-600 text-white">
              <CardTitle className="text-center text-xl">
                Visualization of Predicted Result
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <h3 className="font-semibold">Original Video</h3>
                  <div className="aspect-video bg-black rounded-lg flex items-center justify-center">
                    <video
                      key={apiResponse.video_data_uri}
                      className="w-full h-full rounded-lg"
                      controls
                    >
                      <source
                        src={apiResponse.video_data_uri}
                        type="video/mp4"
                      />
                      Your browser does not support the video tag.
                    </video>
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="font-semibold">Processed Video</h3>
                  <div className="aspect-video bg-black rounded-lg flex items-center justify-center">
                    <video
                      key={apiResponse.predicted_video_url}
                      className="w-full h-full rounded-lg"
                      controls
                    >
                      <source
                        src={apiResponse.predicted_video_url}
                        type="video/mp4"
                      />
                      Your browser does not support the video tag.
                    </video>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Reference Map and Results */}
        {apiResponse && (
          <div className="grid lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>
                    Reference <span className="text-green-600">Map</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="aspect-video bg-green-100 rounded-lg relative overflow-hidden">
                    <img
                      src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/attachments/gen-images/public/wildlife-area-satellite-l65vCimuoAp9JUwDrmU2PBLncKu4QM.png"
                      alt="Reference map showing wildlife area"
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute bottom-4 left-4 bg-white/90 px-3 py-1 rounded text-sm">
                      Slide to See Other Frames
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
            <div>
              <Card>
                <CardHeader>
                  <CardTitle>
                    Result <span className="text-green-600">Table</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Index</TableHead>
                        <TableHead>Frame No.</TableHead>
                        <TableHead>Detected Object</TableHead>
                        <TableHead>Confidence</TableHead>
                        <TableHead>Latitude</TableHead>
                        <TableHead>Longitude</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {resultData.length > 0 ? (
                        resultData.map((row) => (
                          <TableRow key={row.index}>
                            <TableCell>{row.index}</TableCell>
                            <TableCell>{row.frame}</TableCell>
                            <TableCell>{row.object}</TableCell>
                            <TableCell>
                              <Badge variant="secondary">
                                {row.confidence}
                              </Badge>
                            </TableCell>
                            <TableCell>{row.latitude}</TableCell>
                            <TableCell>{row.longitude}</TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center">
                            No detections found.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-3">
                  <feature.icon className="h-6 w-6 text-green-600" />
                </div>
                <CardTitle className="text-lg">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-center text-sm leading-relaxed">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
