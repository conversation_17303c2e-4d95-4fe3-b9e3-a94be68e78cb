import { antonio } from "@/lib/fonts";
import { cn } from "@/lib/utils";
import { AlertTriangle, Eye, Map, MapPin, Users, Video } from "lucide-react";

const features = [
  {
    icon: <Video className="h-6 w-6" />,
    title: "Wildlife Poaching Detection",
    desc: "AI-powered video analysis to detect illegal poaching activities in real-time using computer vision and machine learning.",
  },
  {
    icon: <Users className="h-6 w-6" />,
    title: "Community Reporting",
    desc: "Enable local communities and park rangers to report suspicious activities and wildlife threats instantly.",
  },
  {
    icon: <Map className="h-6 w-6" />,
    title: "Map Visualization",
    desc: "Interactive maps showing wildlife patterns, protected areas, and real-time threat locations for better coordination.",
  },
  {
    icon: <Eye className="h-6 w-6" />,
    title: "Behavioral Tracking",
    desc: "Monitor and analyze wildlife behavior patterns to predict movements and identify potential risks.",
  },
  {
    icon: <AlertTriangle className="h-6 w-6" />,
    title: "Alert Raising",
    desc: "Instant notification system to alert authorities and conservation teams when threats are detected.",
  },
  {
    icon: <MapPin className="h-6 w-6" />,
    title: "Location Tracing",
    desc: "GPS-enabled tracking to pinpoint exact locations of wildlife, threats, and conservation activities.",
  },
];
export default function Feature1() {
  return (
    <section className="relative py-20">
      <div className="mx-auto max-w-screen-xl px-4 md:px-8">
        <div className="relative mx-auto max-w-2xl sm:text-center">
          <div className="relative z-10">
            <h3
              className={cn(
                "mt-4 text-3xl sm:text-4xl md:text-5xl uppercase tracking-tight font-bold",
                antonio.className
              )}
            >
              Let&apos; save the wildlife together
            </h3>
            <p className="text-foreground/60 mt-3">
              Join us in our mission to protect endangered species and preserve
              biodiversity. Together, we can make a difference.
            </p>
          </div>
          <div
            className="absolute inset-0 mx-auto h-44 max-w-xs blur-3xl opacity-60"
            style={{
              background:
                "linear-gradient(152.92deg, rgba(67, 255, 100, 0.3) 4.54%, rgba(67, 255, 100, 0.4) 34.2%, rgba(67, 255, 100, 0.1) 77.55%)",
            }}
          ></div>
        </div>
        <hr className="bg-foreground/30 mx-auto mt-5 h-px w-1/2" />
        <div className="relative mt-12">
          <ul className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {features.map((item, idx) => (
              <li
                key={idx}
                className="transform-all space-y-3 rounded-xl border bg-transparent p-4 md:p-6 [box-shadow:0_-20px_80px_-20px_#c1ffcc_inset]"
              >
                <div className="text-primary w-fit transform-all rounded-full border p-4 [box-shadow:0_-20px_80px_-20px_#c1ffcc_inset] dark:[box-shadow:0_-20px_80px_-20px_#c1ffcc_inset]">
                  {item.icon}
                </div>
                <h4 className="text-lg font-bold">{item.title}</h4>
                <p className="text-gray-500">{item.desc}</p>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </section>
  );
}
