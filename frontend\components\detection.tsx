/* eslint-disable @next/next/no-img-element */
"use client";

import type React from "react";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  Upload,
  Video,
  Shield,
  Zap,
  Eye,
  MapPin,
  AlertTriangle,
  Car,
  Camera,
  Target,
} from "lucide-react";

export default function WildlifeDetection() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [timeOfDay, setTimeOfDay] = useState("");

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const features = [
    {
      icon: Eye,
      title: "Stealth Surveillance",
      description:
        "Efficient monitoring system that operates covertly to safeguard wildlife habitats.",
    },
    {
      icon: Shield,
      title: "Firefighter AI",
      description:
        "Cutting-edge technology that detects and responds to wildfires in real-time, minimizing environmental damage.",
    },
    {
      icon: Target,
      title: "Poacher Buster",
      description:
        "Dedicated AI solution designed to capture illegal poaching activities and protect endangered species.",
    },
    {
      icon: Camera,
      title: "Night Guardian",
      description:
        "Vigilant nighttime surveillance system using thermal imaging to safeguard wildlife during the dark hours.",
    },
    {
      icon: MapPin,
      title: "Wildlife Watcher",
      description:
        "Intelligent observer that monitors and studies the behavior of diverse wildlife species.",
    },
    {
      icon: Zap,
      title: "Smart Sentry",
      description:
        "Intelligent sentinels that detect and report intrusions or threats to wildlife habitats.",
    },
    {
      icon: AlertTriangle,
      title: "Anomaly Alert",
      description:
        "Rapid detection and alert system for suspicious activities, enhancing wildlife protection.",
    },
    {
      icon: Car,
      title: "Vehicle Vigilant",
      description:
        "Advanced system that monitors unauthorized vehicles in protected wildlife areas.",
    },
  ];

  const resultData = [
    { index: 1, frame: "Frame 1", object: "Elephant", confidence: "95.2%" },
    { index: 2, frame: "Frame 2", object: "Poacher", confidence: "87.8%" },
    { index: 3, frame: "Frame 3", object: "Vehicle", confidence: "92.1%" },
  ];

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-4xl font-bold">
            <span className="text-green-600">Wildlife</span> Anomaly Detection
            Using YOLO
          </h1>
        </div>

        {/* Upload Section */}
        <Card className="w-full max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Upload Video
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="video-upload">
                Upload Video (.mp4, .avi, .mov)
              </Label>
              <div className="flex items-center gap-4">
                <Input
                  id="video-upload"
                  type="file"
                  accept="video/*"
                  onChange={handleFileChange}
                  className="flex-1"
                />
                <Badge variant="outline" className="flex items-center gap-1">
                  <Video className="h-3 w-3" />
                  Alert & Notification
                </Badge>
              </div>
              {selectedFile && (
                <p className="text-sm text-muted-foreground">
                  Selected: {selectedFile.name}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="time-select">Time of Day</Label>
              <Select value={timeOfDay} onValueChange={setTimeOfDay}>
                <SelectTrigger>
                  <SelectValue placeholder="Day/Night" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="day">Day</SelectItem>
                  <SelectItem value="night">Night</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button
              className="w-full bg-green-600 hover:bg-green-700"
              size="lg"
            >
              Upload
            </Button>
          </CardContent>
        </Card>

        {/* Visualization Section */}
        <Card>
          <CardHeader className="bg-green-600 text-white">
            <CardTitle className="text-center text-xl">
              Visualization of Predicted Result
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <h3 className="font-semibold">Original Video</h3>
                <div className="aspect-video bg-black rounded-lg flex items-center justify-center">
                  <video
                    className="w-full h-full rounded-lg"
                    controls
                    poster="/placeholder-nfyz5.png"
                  >
                    <source src="#" type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                </div>
              </div>
              <div className="space-y-2">
                <h3 className="font-semibold">Processed Video</h3>
                <div className="aspect-video bg-black rounded-lg flex items-center justify-center">
                  <video
                    className="w-full h-full rounded-lg"
                    controls
                    poster="/ai-wildlife-detection.png"
                  >
                    <source src="#" type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Reference Map and Results */}
        <div className="grid lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>
                  Reference <span className="text-green-600">Map</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="aspect-video bg-green-100 rounded-lg relative overflow-hidden">
                  <img
                    src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/attachments/gen-images/public/wildlife-area-satellite-l65vCimuoAp9JUwDrmU2PBLncKu4QM.png"
                    alt="Reference map showing wildlife area"
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute bottom-4 left-4 bg-white/90 px-3 py-1 rounded text-sm">
                    Slide to See Other Frames
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card>
              <CardHeader>
                <CardTitle>
                  Result <span className="text-green-600">Table</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Index</TableHead>
                      <TableHead>Frame No.</TableHead>
                      <TableHead>Detected Object</TableHead>
                      <TableHead>Confidence</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {resultData.map((row) => (
                      <TableRow key={row.index}>
                        <TableCell>{row.index}</TableCell>
                        <TableCell>{row.frame}</TableCell>
                        <TableCell>{row.object}</TableCell>
                        <TableCell>
                          <Badge variant="secondary">{row.confidence}</Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-3">
                  <feature.icon className="h-6 w-6 text-green-600" />
                </div>
                <CardTitle className="text-lg">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-center text-sm leading-relaxed">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
