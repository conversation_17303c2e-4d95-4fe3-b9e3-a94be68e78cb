"use client";
import Feature1 from "@/components/shared/Features";
import Footer4Col from "@/components/shared/Footer";
import SimpleNavbar from "@/components/shared/Navbar";
import { antonio } from "@/lib/fonts";
import { cn } from "@/lib/utils";
import Image from "next/image";

export default function Home() {
  return (
    <>
      <SimpleNavbar
        logoSrc="/logo.png"
        leftButtonText="Menu"
        rightButtonText="Sign in"
        buttonBgColor="bg-[#c1ffcc]"
        buttonHoverBgColor="after:bg-primary-foreground"
        buttonTextColor="text-primary-foreground"
        buttonHoverTextColor="after:text-[#baffcb]"
        buttonContentClassName={`font-extrabold uppercase !text-2xl`}
        onRightButtonClick={() => {
          window.location.href = "/register";
        }}
        onLeftButtonClick={() => {
          window.location.href = "/dashboard";
        }}
        buttonClassName="h-13 px-8 border-2 border-black"
        className="backdrop-blur-xl"
        logoClassName="h-24 brightness-[40%]"
      />
      <section className="w-full relative overflow-hidden h-svh md:h-[130vh] bg-[#cfffd7]">
        <div className="max-w-7xl px-4 sm:px-10 w-full h-svh flex flex-col items-center relative mx-auto z-20 pt-[35%] md:pt-0 md:justify-center">
          <div
            className={cn(
              "w-full flex flex-col text-center items-center md:!max-w-3/4 gap-6 md:gap-10",
              antonio.className
            )}
          >
            <h1
              className={`text-5xl md:text-[clamp(5rem,7vw,8rem)] mx-auto text-center tracking-tight text-white font-extrabold uppercase ${antonio.className} stroked-text`}
            >
              Save the Wildlife from Poachers
            </h1>
            <div
              className={`w-full max-w-3xl mx-auto md:text-2xl text-base font-bold leading-[1.1] text-gray-800`}
            >
              Join us in our mission to protect endangered species and preserve
              biodiversity. Together, we can make a difference.
            </div>
          </div>
        </div>
        <div className="md:w-screen w-[220vw] absolute z-0 bottom-0 left-1/2 -translate-x-1/2">
          <Image
            priority
            width={1920}
            height={1080}
            src="/image.png"
            alt="Background decoration"
            className="w-full h-full [mask-image:linear-gradient(to_bottom,transparent_0%,black_30%,black_80%,transparent_100%,)] [-webkit-mask-image:linear-gradient(to_bottom,transparent_0%,black_40%,black_80%,transparent_100%)]"
          />
        </div>
      </section>

      <Feature1 />

      <Footer4Col />
    </>
  );
}
