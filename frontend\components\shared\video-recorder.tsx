"use client";

import { useEffect, useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import { Camera, Maximize, StopCircle, VideoIcon } from "lucide-react";
import { toast } from "sonner";

interface VideoRecorderProps {
  onRecordingComplete?: (blob: Blob, coords: GeolocationCoordinates | null) => void;
  onRecordingStop?: () => void;
}

export default function VideoRecorder({ onRecordingComplete, onRecordingStop }: VideoRecorderProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [coords, setCoords] = useState<GeolocationCoordinates | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

    const startCamera = async () => {
    try {
      setError(null);
      console.log('📹 Starting camera with high-quality settings...');
      
      const constraints = {
        video: {
          width: { ideal: 1280, min: 640 }, // Reduced from 1920x1080
          height: { ideal: 720, min: 480 }, // Reduced for better performance
          frameRate: { ideal: 24, min: 15 }, // Reduced from 30fps to 24fps
          facingMode: 'environment' // Back camera for mobile
        },
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 22050 // Reduced from 44100 for better performance
        }
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      console.log('✅ Camera stream acquired:', stream.getVideoTracks()[0].getSettings());
      console.log('✅ Audio stream acquired:', stream.getAudioTracks()[0].getSettings());
      
      setStream(stream);
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    } catch (error) {
      console.error('❌ Error accessing camera:', error);
      setError('Failed to access camera or microphone');
      throw error;
    }
  };

  useEffect(() => {
    startCamera();
    
    // Listen for fullscreen changes
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };
    
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    
    return () => {
      if (stream) {
        stream.getTracks().forEach((track) => track.stop());
      }
      // Capture the current timer value for cleanup
      const currentTimer = timerRef.current;
      if (currentTimer) {
        clearInterval(currentTimer);
      }
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getGeolocation = () => {
    return new Promise<GeolocationCoordinates | null>((resolve) => {
      if (navigator.geolocation) {
        console.log('🌍 Requesting precise location for recording...');
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const coords = position.coords;
            setCoords(coords);
            console.log('✅ Location acquired for recording:', coords.latitude, coords.longitude);
            console.log('📍 Accuracy:', coords.accuracy, 'meters');
            console.log('📍 Altitude:', coords.altitude);
            console.log('📍 Speed:', coords.speed);
            resolve(coords);
          },
          (error) => {
            console.error("❌ Error getting geolocation:", error.message);
            console.error("❌ Error code:", error.code);
            setCoords(null);
            resolve(null);
          },
          {
            enableHighAccuracy: true,
            timeout: 15000, // 15 seconds
            maximumAge: 30000 // 30 seconds cache for recording
          }
        );
      } else {
        console.error("❌ Geolocation is not supported by this browser.");
        setCoords(null);
        resolve(null);
      }
    });
  };

  const uploadChunk = async (chunk: Blob) => {
    if (onRecordingComplete) {
      console.log(`📦 Processing final video, size: ${chunk.size} bytes, type: ${chunk.type}`);
      
      // Show notification for video processing
      toast.success("Processing your video...", {
        description: `Uploading ${(chunk.size / 1024 / 1024).toFixed(2)}MB to cloud storage`,
        duration: 4000,
      });
      
      try {
        await onRecordingComplete(chunk, coords);
        console.log(`✅ Video upload completed successfully`);
        
        toast.success("Video saved successfully!", {
          description: "Your recording has been uploaded to the database",
          duration: 3000,
        });
        
      } catch (error) {
        console.error(`❌ Video upload failed:`, error);
        toast.error("Upload failed!", {
          description: "Upload error - check connection and try again",
          duration: 5000,
        });
      }
    }
  };

  const handleStartRecording = async () => {
    if (stream) {
      // Get location when recording starts
      await getGeolocation();
      
      setIsRecording(true);
      setRecordingTime(0);
      
      // Start the timer
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
      
      // Use supported format for better compatibility
      let mimeType = "video/webm;codecs=vp8,opus";
      if (!MediaRecorder.isTypeSupported(mimeType)) {
        mimeType = "video/webm;codecs=vp8";
        if (!MediaRecorder.isTypeSupported(mimeType)) {
          mimeType = "video/webm";
          if (!MediaRecorder.isTypeSupported(mimeType)) {
            // Fallback to MP4 if WebM is not supported
            mimeType = "video/mp4";
            console.log('⚠️ WebM not supported, falling back to MP4');
          }
        }
      }
      
      console.log('Using MIME type:', mimeType);
      
      const recorder = new MediaRecorder(stream, { 
        mimeType: mimeType,
        audioBitsPerSecond: 64000, // Reduced for better performance
        videoBitsPerSecond: 2500000 // Reduced for better performance
      });
      mediaRecorderRef.current = recorder;
      
      // Store all recorded data for final upload
      const recordedChunks: Blob[] = [];
      
      recorder.ondataavailable = (event) => {
        console.log('📊 MediaRecorder data available:', event.data.size, 'bytes, type:', event.data.type);
        if (event.data.size > 0) {
          recordedChunks.push(event.data);
        }
      };
      
      recorder.onstop = () => {
        console.log('🛑 MediaRecorder stopped, processing final video...');
        if (recordedChunks.length > 0) {
          // Combine all chunks into one final blob
          const finalBlob = new Blob(recordedChunks, { type: mimeType });
          console.log('✅ Final video created:', finalBlob.size, 'bytes');
          uploadChunk(finalBlob);
        }
      };
      
      recorder.onerror = (event) => {
        console.error('❌ MediaRecorder error:', event);
        toast.error("Recording error occurred", {
          description: "Please try starting the recording again",
        });
      };

      // Start recording - no chunking, record everything until stopped
      recorder.start();
      console.log('🎬 MediaRecorder started - recording complete video');
      
      toast.success("Recording started!", {
        description: "Recording will save when you stop",
        duration: 3000,
      });
    }
  };

  const handleStopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      console.log('Stopping MediaRecorder...');
      
      // Stop recording - this will trigger onstop event and final upload
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      
      // Clear the timer
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
      
      // Call the onRecordingStop callback if provided
      if (onRecordingStop) {
        onRecordingStop();
      }
      
      toast.success("Recording stopped!", {
        description: "Processing and uploading your video...",
        duration: 4000,
      });
      
      console.log(`Recording stopped. Total time: ${formatTime(recordingTime)}`);
      
      // Don't stop the stream - let user record again if they want
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleFullScreen = async () => {
    try {
      if (containerRef.current) {
        if (isFullscreen) {
          await document.exitFullscreen();
        } else {
          await containerRef.current.requestFullscreen();
        }
      }
    } catch (err) {
      console.error("Error toggling fullscreen:", err);
    }
  };

  if (error) {
    return (
      <div className="text-center p-8 bg-red-50 dark:bg-red-900/20 rounded-lg">
        <p className="text-red-600 dark:text-red-400">{error}</p>
        <Button onClick={startCamera} className="mt-4">Try Again</Button>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center gap-4 w-full">
      <div 
        ref={containerRef}
        className={`w-full max-w-6xl aspect-video relative rounded-lg overflow-hidden shadow-2xl bg-black group transition-all duration-300 ${
          isFullscreen ? 'fixed inset-0 z-50 max-w-none rounded-none aspect-auto' : ''
        }`}
      >
        <video
          ref={videoRef}
          autoPlay
          playsInline
          muted
          className={`w-full h-full object-cover ${isFullscreen ? 'object-contain' : ''}`}
        />
        {!isRecording && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/50">
            <div className="text-center">
              <Camera className="w-16 h-16 text-white/50 mx-auto mb-4" />
              <p className="text-white/70 text-lg">
                {isFullscreen ? "Full Screen Mode - Tap record to start" : "Tap fullscreen for landscape recording"}
              </p>
            </div>
          </div>
        )}
        
        {/* Fullscreen toggle button */}
        <Button
          size="icon"
          variant="ghost"
          onClick={handleFullScreen}
          className="absolute top-4 right-4 text-white bg-black/30 hover:bg-black/50 transition-colors"
          aria-label={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
        >
          <Maximize className="w-6 h-6" />
        </Button>

        {/* Recording indicator with timer */}
        {isRecording && (
          <div className="absolute top-4 left-4 flex items-center gap-3">
            <div className="flex items-center gap-2 bg-red-500 text-white px-3 py-1 rounded-full">
              <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
              <span className="text-sm font-semibold">REC</span>
            </div>
            <div className="bg-black/70 text-white px-3 py-1 rounded-full">
              <span className="text-sm font-mono font-semibold">{formatTime(recordingTime)}</span>
            </div>
          </div>
        )}

        {/* Location indicator */}
        {coords && (
          <div className="absolute bottom-4 left-4 bg-black/70 text-white text-xs px-3 py-2 rounded-lg border border-green-400">
            <div className="flex items-center gap-1">
              <span className="text-green-400">📍</span>
              <span>Location: {coords.latitude.toFixed(4)}, {coords.longitude.toFixed(4)}</span>
            </div>
          </div>
        )}

        {/* Fullscreen recording controls */}
        {isFullscreen && (
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
            <Button
              onClick={isRecording ? handleStopRecording : handleStartRecording}
              disabled={!stream}
              className={`w-20 h-20 rounded-full text-lg font-bold transition-all duration-300 ease-in-out flex items-center justify-center shadow-lg
                ${isRecording 
                  ? "bg-red-500 hover:bg-red-600 text-white animate-pulse" 
                  : "bg-blue-500 hover:bg-blue-600 text-white"
                }`}
            >
              {isRecording ? (
                <StopCircle className="w-10 h-10" />
              ) : (
                <VideoIcon className="w-10 h-10" />
              )}
            </Button>
          </div>
        )}
      </div>

      {/* Regular controls when not in fullscreen */}
      {!isFullscreen && (
        <div className="flex items-center justify-center gap-4 w-full">
          <Button
            onClick={isRecording ? handleStopRecording : handleStartRecording}
            disabled={!stream}
            className={`w-48 h-16 rounded-full text-lg font-bold transition-all duration-300 ease-in-out flex items-center justify-center gap-2 shadow-lg
              ${isRecording 
                ? "bg-red-500 hover:bg-red-600 text-white animate-pulse" 
                : "bg-blue-500 hover:bg-blue-600 text-white"
              }`}
          >
            {isRecording ? (
              <>
                <StopCircle className="w-8 h-8" /> Stop Recording
              </>
            ) : (
              <>
                <VideoIcon className="w-8 h-8" /> Start Recording
              </>
            )}
          </Button>
        </div>
      )}
      
      {/* Real-time upload status */}
      {isRecording && !isFullscreen && (
        <div className="text-center">
          <p className="text-sm text-green-600 font-medium">
            🔴 Recording in progress - Video will upload when you stop recording
          </p>
          <p className="text-xs text-gray-500 mt-1">
            Recording time: {formatTime(recordingTime)}
          </p>
        </div>
      )}
    </div>
  );
}
