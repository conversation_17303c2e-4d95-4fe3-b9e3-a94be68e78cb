import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google";

export const geist = <PERSON>eist({
  weight: ["400", "500", "600", "700", "800", "900"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-geist",
});

export const antonio = <PERSON>({
  weight: ["100", "200", "300", "400", "500", "600", "700"],
  subsets: ["latin"],
  variable: "--font-antonio",
});

export const barlow = <PERSON>({
  weight: ["400", "500", "600", "700", "800", "900"],
  subsets: ["latin"],
  variable: "--font-barlow",
});
