"""
Alert analysis service for determining poaching detection status.
"""

from typing import List, Dict, Any, <PERSON><PERSON>

from ..config.settings import ALERT_CLASSES


class AlertAnalysisService:
    """Analyzes wildlife detections to determine alert status and poaching indicators."""

    def __init__(self):
        self.poaching_classes = {"Person", "Vehicle"}  # Classes that indicate poaching
        self.fire_classes = {"Fire"}  # Classes that indicate fire

    def analyze_detections(
        self, result_list: List[Dict[str, Any]]
    ) -> Tuple[Dict[str, List[Dict[str, Any]]], str, bool]:
        """
        Analyze detection results and return detailed alert information.

        Args:
            result_list: List of frame detection results

        Returns:
            tuple: (alert_classes_with_confidence, primary_alert_class, is_poaching_detected)
        """
        # Dictionary to store confidence scores for each alert class
        alert_class_confidences = {}

        # Collect all detected classes and their confidence scores from all frames
        for frame in result_list:
            detections = frame.get("detections", [])
            for detection in detections:
                class_name = detection.get("class_name")
                confidence = detection.get("confidence", 0.0)

                if class_name and class_name in ALERT_CLASSES:
                    if class_name not in alert_class_confidences:
                        alert_class_confidences[class_name] = []
                    alert_class_confidences[class_name].append(confidence)

        # Calculate mean confidence for each alert class and prepare detailed response
        alert_classes_detailed = {}
        detected_classes = set(alert_class_confidences.keys())

        for class_name, confidences in alert_class_confidences.items():
            mean_confidence = sum(confidences) / len(confidences)
            alert_classes_detailed[class_name] = [
                {
                    "class_name": class_name,
                    "mean_confidence": round(mean_confidence, 3),
                    "detection_count": len(confidences),
                    "confidence_scores": [round(c, 3) for c in confidences],
                }
            ]

        # Determine primary alert class priority (Person > Vehicle > Fire)
        primary_alert_class = "None"
        if "Person" in detected_classes:
            primary_alert_class = "Person"
        elif "Vehicle" in detected_classes:
            primary_alert_class = "Vehicle"
        elif "Fire" in detected_classes:
            primary_alert_class = "Fire"

        # Determine if poaching is detected (Person or Vehicle)
        is_poaching_detected = bool(detected_classes & self.poaching_classes)

        return alert_classes_detailed, primary_alert_class, is_poaching_detected

    def process_alerts(self, result_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Process results and return detailed alert information.

        Args:
            result_list: List of frame detection results

        Returns:
            dict: Detailed alert information with all detected classes and their confidence scores
        """
        alert_classes_detailed, primary_alert_class, is_poaching_detected = (
            self.analyze_detections(result_list)
        )

        return {
            "alert_classes": alert_classes_detailed,
            "primary_alert_class": primary_alert_class,
            "is_poaching_detected": is_poaching_detected,
            # Keep backward compatibility
            "alert_class": primary_alert_class,
        }


# Global alert analysis service instance
notification_service = AlertAnalysisService()
