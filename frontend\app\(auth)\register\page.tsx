import AuthForm from "@/components/AuthForm";
import Link from "next/link";
import Image from "next/image";

export default function RegisterPage() {
  return (
    <div className="grid grid-cols-2 max-h-screen items-center justify-center bg-background w-full">
      <div className="flex w-full flex-col gap-6 items-center justify-center">
        <AuthForm />
      </div>
      <div className="hidden md:block h-screen overflow-hidden w-full relative">
        <Link
          href="/"
          className="flex items-center gap-2 self-center font-medium absolute text-primary left-1/2 -translate-x-1/2 top-4 text-xl"
        >
          <Image
            src="/logo.png"
            alt="logo"
            width={100}
            height={100}
            className="h-12 w-12"
          />
          Wild Eye
        </Link>
        <Image
          src={
            "https://images.unsplash.com/photo-1549366021-9f761d450615?q=80&w=706&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
          }
          width={800}
          height={800}
          alt=""
          className="object-cover h-full w-full"
        />
      </div>
    </div>
  );
}
