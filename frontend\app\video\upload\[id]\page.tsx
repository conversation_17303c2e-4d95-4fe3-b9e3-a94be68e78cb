import Videoupload from "@/components/shared/Videoupload";
import { db } from "@/lib/db";
import { <PERSON>, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { XCircle } from "lucide-react";

export default async function PublicUploadPage({ params }: { params: Promise<{ id: string }> }) {
  const sessionId = (await params).id;
  
  // Check if the session exists and is valid
  const uploadSession = await db.uploadSession.findUnique({
    where: { id: sessionId },
  });

  // If session doesn't exist or is expired, show expired message
  if (!uploadSession || uploadSession.status === "EXPIRED") {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background dark:bg-gray-900">
        <Card className="w-full max-w-md text-center p-8 shadow-2xl">
          <CardHeader>
            <XCircle className="mx-auto h-16 w-16 text-orange-500" />
            <CardTitle className="text-3xl font-bold mt-4">Session Expired</CardTitle>
            <CardDescription className="pt-2 text-lg">
              This recording session has expired or been completed. Please generate a new QR code to start recording again.
            </CardDescription>
            <div className="mt-6 p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
              <p className="text-sm text-orange-600 dark:text-orange-400 font-medium">
                🔒 For security, upload sessions are automatically expired after use.
              </p>
            </div>
          </CardHeader>
        </Card>
      </div>
    );
  }

  // If session is COMPLETE, show a completion message instead of the uploader
  if (uploadSession.status === "COMPLETE") {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background dark:bg-gray-900">
        <Card className="w-full max-w-md text-center p-8 shadow-2xl">
          <CardHeader>
            <div className="mx-auto h-16 w-16 text-green-500 mb-4">✅</div>
            <CardTitle className="text-3xl font-bold mt-4">Upload Complete!</CardTitle>
            <CardDescription className="pt-2 text-lg">
              Your video has been successfully uploaded and saved. This session has been completed.
            </CardDescription>
            <div className="mt-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <p className="text-sm text-green-600 dark:text-green-400 font-medium">
                ✨ Your recording is now available in the video gallery.
              </p>
            </div>
          </CardHeader>
        </Card>
      </div>
    );
  }

  // Check if session is too old (24 hours)
  const sessionAge = Date.now() - uploadSession.createdAt.getTime();
  const maxAge = 24 * 60 * 60 * 1000; // 24 hours
  if (sessionAge > maxAge) {
    // Mark as expired in database
    await db.uploadSession.update({
      where: { id: sessionId },
      data: { status: "EXPIRED" }
    });
    
    return (
      <div className="flex items-center justify-center min-h-screen bg-background dark:bg-gray-900">
        <Card className="w-full max-w-md text-center p-8 shadow-2xl">
          <CardHeader>
            <XCircle className="mx-auto h-16 w-16 text-orange-500" />
            <CardTitle className="text-3xl font-bold mt-4">Session Expired</CardTitle>
            <CardDescription className="pt-2 text-lg">
              This recording session has expired due to inactivity (24 hours). Please generate a new QR code to start recording again.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <Videoupload sessionId={sessionId} />
  );
}
