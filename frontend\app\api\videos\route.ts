import { NextResponse } from "next/server";
import { db } from "@/lib/db";

export async function GET(request: Request) {
  console.log('=== VIDEOS API DEBUG START ===');
  
  try {
    const url = new URL(request.url);
    const sessionId = url.searchParams.get("sessionId");
    
    console.log('📝 Requested session ID:', sessionId);

    // Optional: Add authentication if you want to restrict video viewing
    // const session = await auth();
    // if (!session?.user?.id) {
    //   return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    // }

    console.log('🚀 Querying database for videos...');
    
    const videos = await db.video.findMany({
      where: {
        uploadSessionId: sessionId ?? undefined,
      },
      orderBy: {
        createdAt: "desc",
      },
      include: {
        user: {
          select: {
            name: true,
            image: true,
          },
        },
      },
    });
    
    console.log('📝 Found videos:', videos.length);
    console.log('📝 Video details:', videos.map(v => ({
      id: v.id,
      title: v.title,
      createdAt: v.createdAt,
      latitude: v.latitude,
      longitude: v.longitude,
      sessionId: v.uploadSessionId
    })));
    
    console.log('=== VIDEOS API DEBUG END ===');
    
    return NextResponse.json(videos);
  } catch (error) {
    console.error("💥 Get Videos API Error:", error);
    console.log('=== VIDEOS API DEBUG END (ERROR) ===');
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
