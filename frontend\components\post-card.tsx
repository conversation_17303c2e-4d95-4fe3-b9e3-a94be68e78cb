"use client";

import { useState } from "react";
import Image from "next/image";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar } from "@/components/ui/avatar";
import { likePost, deletePost } from "@/actions/post.actions";
import { Heart, Trash2, User as UserIcon } from "lucide-react";
import { User } from "@prisma/client";

type PostWithUser = {
  id: string;
  title: string;
  content: string;
  likes: number;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  user: {
    id: string;
    name: string | null;
    email: string | null;
    image: string | null;
  };
};

interface PostCardProps {
  post: PostWithUser;
  currentUser?: User;
  onPostDeleted?: () => void;
}

export function PostCard({ post, currentUser, onPostDeleted }: PostCardProps) {
  const [isLiking, setIsLiking] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [likes, setLikes] = useState(post.likes);
  const [message, setMessage] = useState("");

  const handleLike = async () => {
    if (isLiking) return;

    setIsLiking(true);
    setMessage("");

    try {
      const result = await likePost(post.id);

      if (result.success) {
        setLikes((prev) => prev + 1);
        setMessage("Post liked!");
      } else {
        setMessage(result.error || "Failed to like post");
      }
    } catch {
      setMessage("An unexpected error occurred");
    } finally {
      setIsLiking(false);
    }
  };

  const handleDelete = async () => {
    if (!currentUser || isDeleting) return;

    if (!confirm("Are you sure you want to delete this post?")) return;

    setIsDeleting(true);
    setMessage("");

    try {
      const result = await deletePost(post.id, currentUser.id);

      if (result.success) {
        setMessage("Post deleted successfully!");
        onPostDeleted?.();
      } else {
        setMessage(result.error || "Failed to delete post");
      }
    } catch {
      setMessage("An unexpected error occurred");
    } finally {
      setIsDeleting(false);
    }
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(
        (now.getTime() - date.getTime()) / (1000 * 60)
      );
      return diffInMinutes < 1 ? "Just now" : `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  const canDelete = currentUser && currentUser.id === post.userId;

  return (
    <Card className="border-border bg-card">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="w-10 h-10">
              {post.user.image ? (
                <Image
                  src={post.user.image}
                  alt={post.user.name || "User"}
                  width={40}
                  height={40}
                  className="w-full h-full object-cover rounded-full"
                />
              ) : (
                <div className="w-full h-full bg-muted flex items-center justify-center rounded-full">
                  <UserIcon className="w-5 h-5 text-muted-foreground" />
                </div>
              )}
            </Avatar>
            <div className="flex flex-col">
              <h3 className="text-sm font-semibold text-foreground">
                {post.user.name || post.user.email || "Anonymous"}
              </h3>
              <p className="text-xs text-muted-foreground">
                {formatDate(new Date(post.createdAt))}
              </p>
            </div>
          </div>

          {canDelete && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDelete}
              disabled={isDeleting}
              className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive hover:bg-destructive/10"
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="space-y-3">
          <div>
            <h2 className="text-lg font-semibold text-foreground mb-2">
              {post.title}
            </h2>
            <p className="text-foreground leading-relaxed whitespace-pre-wrap">
              {post.content}
            </p>
          </div>

          {message && (
            <div
              className={`text-xs p-2 rounded-md ${
                message.includes("success") || message.includes("liked")
                  ? "bg-primary/10 text-primary border border-primary/20"
                  : "bg-destructive/10 text-destructive border border-destructive/20"
              }`}
            >
              {message}
            </div>
          )}

          <div className="flex items-center justify-between pt-2 border-t border-border">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLike}
              disabled={isLiking}
              className="flex items-center space-x-2 text-muted-foreground hover:text-primary hover:bg-primary/10"
            >
              <Heart className={`w-4 h-4 ${isLiking ? "animate-pulse" : ""}`} />
              <span className="text-sm">{likes}</span>
            </Button>

            <div className="text-xs text-muted-foreground">
              Forest Community
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
