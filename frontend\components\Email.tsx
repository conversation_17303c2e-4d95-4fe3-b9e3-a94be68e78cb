import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { AlertTriangle, MapPin, Clock, ExternalLink, Shield } from "lucide-react"

interface PoachingAlertEmailProps {
  latitude: number
  longitude: number
  locationName: string
  detectedAt: string
}

export function PoachingAlertEmail({
  latitude,
  longitude,
  locationName,
  detectedAt,
}: PoachingAlertEmailProps) {
  const googleMapsUrl = `https://www.google.com/maps?q=${latitude},${longitude}`
  const formattedDate = new Date(detectedAt).toLocaleString()

  return (
    <div className="max-w-2xl mx-auto bg-white font-sans shadow-lg rounded-lg overflow-hidden">
      <div className="bg-red-600 text-white p-6 text-center">
        <div className="flex items-center justify-center gap-3 mb-2">
          <AlertTriangle className="h-8 w-8" />
          <h1 className="text-2xl font-bold">POACHING ALERT</h1>
        </div>
        <p className="text-red-100">Wildlife Protection System</p>
      </div>

      <div className="p-6">
        <Card className="border-red-300 bg-red-50 mb-6">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <AlertTriangle className="h-6 w-6 text-red-600 flex-shrink-0 mt-1" />
              <div>
                <h2 className="text-xl font-bold text-red-800 mb-2">Poaching Activity Detected</h2>
                <p className="text-red-700">
                  Suspicious activity has been detected in the protected area. Immediate response required.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="space-y-4 mb-6">
          <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
            <MapPin className="h-5 w-5 text-gray-600" />
            <div>
              <p className="font-semibold text-gray-900">Location</p>
              <p className="text-gray-700">{locationName}</p>
            </div>
          </div>

          <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
            <div className="h-5 w-5 text-gray-600 font-bold text-sm flex items-center justify-center">GPS</div>
            <div>
              <p className="font-semibold text-gray-900">Coordinates</p>
              <p className="text-gray-700 font-mono">
                {latitude.toFixed(6)}, {longitude.toFixed(6)}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
            <Clock className="h-5 w-5 text-gray-600" />
            <div>
              <p className="font-semibold text-gray-900">Detection Time</p>
              <p className="text-gray-700">{formattedDate}</p>
            </div>
          </div>
        </div>

        <div className="mb-6">
          <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700 text-white w-full">
            <a
              href={googleMapsUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center justify-center gap-2"
            >
              <MapPin className="h-5 w-5" />
              View Location on Google Maps
              <ExternalLink className="h-4 w-4" />
            </a>
          </Button>
        </div>

        <div className="text-center bg-gray-50 rounded-lg p-4 border-t-2 border-gray-200">
          <div className="flex items-center justify-center gap-2 mb-2">
            <Shield className="h-4 w-4 text-gray-600" />
            <p className="font-semibold text-gray-700 text-sm">Wildlife Protection System</p>
          </div>
          <p className="text-gray-600 text-xs">Automated alert - Contact authorities immediately</p>
        </div>
      </div>
    </div>
  )
}
