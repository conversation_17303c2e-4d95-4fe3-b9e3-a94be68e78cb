import { PoachingAlertEmail } from "@/components/Email";
import { Resend } from "resend";

const resend = new Resend(process.env.RESEND_API_KEY);

export async function POST(request: Request) {
  const body = await request.json();

  const { latitude, longitude, locationName } = body;

  try {
    const { data, error } = await resend.emails.send({
      from: "WildEye <<EMAIL>>",
      to: ["<EMAIL>"],
      subject: "Red Alert for Poaching",
      react: PoachingAlertEmail({
        latitude,
        longitude,
        locationName,
        detectedAt: new Date().toISOString(),
      }),
    });

    if (error) {
      return Response.json({ error }, { status: 500 });
    }

    return Response.json(data);
  } catch (error) {
    return Response.json({ error }, { status: 500 });
  }
}
